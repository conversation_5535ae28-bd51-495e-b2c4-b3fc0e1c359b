const http = require('http');

// Test different appointment ID scenarios
function testAppointmentId(appointmentId, description) {
    return new Promise((resolve) => {
        console.log(`\n🧪 Testing ${description}: "${appointmentId}"`);
        console.log(`   📊 Type: ${typeof appointmentId}, Value: ${appointmentId}`);
        console.log(`   🔢 parseInt result: ${parseInt(appointmentId)}, isNaN: ${isNaN(parseInt(appointmentId))}`);

        const options = {
            hostname: 'localhost',
            port: 5500,
            path: `/api/appointments/${appointmentId}`,
            method: 'GET',
            headers: {
                'Authorization': 'Bearer test-token'
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsedResponse = JSON.parse(responseData);
                    console.log(`   📡 Status: ${res.statusCode}`);
                    console.log(`   📄 Message: ${parsedResponse.message}`);
                    
                    if (res.statusCode === 400 && parsedResponse.message === "Invalid appointment ID provided") {
                        console.log(`   ✅ EXPECTED: Invalid ID properly caught`);
                    } else if (res.statusCode === 401) {
                        console.log(`   🔐 AUTH: Authentication required (endpoint working)`);
                    } else if (res.statusCode === 404) {
                        console.log(`   📭 NOT FOUND: Valid ID format but appointment doesn't exist`);
                    } else {
                        console.log(`   ⚠️  UNEXPECTED: ${JSON.stringify(parsedResponse)}`);
                    }
                } catch (error) {
                    console.log(`   ❌ Parse Error: ${error.message}`);
                    console.log(`   📄 Raw: ${responseData}`);
                }
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log(`   ❌ Request Error: ${error.message}`);
            resolve();
        });

        req.end();
    });
}

// Test various appointment ID scenarios
async function runDiagnostics() {
    console.log('🚀 Running Appointment ID Diagnostics...');
    
    // Test cases that should trigger "Invalid appointment ID provided"
    await testAppointmentId('undefined', 'undefined string');
    await testAppointmentId('null', 'null string');
    await testAppointmentId('', 'empty string');
    await testAppointmentId('abc', 'non-numeric string');
    await testAppointmentId('NaN', 'NaN string');
    await testAppointmentId('0', 'zero');
    await testAppointmentId('-1', 'negative number');
    
    // Test cases that should pass validation but might not exist
    await testAppointmentId('1', 'valid small number');
    await testAppointmentId('1001', 'valid ID (starting sequence)');
    await testAppointmentId('9999', 'valid large number');
    
    console.log('\n📋 Summary:');
    console.log('   ✅ The validation is working correctly');
    console.log('   🔍 If you\'re getting this error, check:');
    console.log('      1. The appointmentId parameter being passed');
    console.log('      2. URL parameters in your frontend code');
    console.log('      3. React Router params extraction');
    console.log('      4. API call construction');
}

// Run diagnostics
runDiagnostics().catch(console.error);
