import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, Clock, User, PawPrint, AlertTriangle, CheckCircle } from "lucide-react";
import { useAuth } from "@/store";
import { api } from "@/services/api";
import { toast } from "@/components/ui/use-toast";

interface UnifiedBookingFormData {
  clientType: 'registered' | 'walk-in';
  clientData: {
    clientId?: number;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
  };
  petData: {
    petId?: number;
    petName: string;
    species: string;
    breed: string;
    gender: string;
    age: number;
    weight: number;
  };
  appointmentData: {
    appointmentDate: string;
    appointmentTime: string;
    categories: number[];
    notes: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
    estimatedDuration: number;
  };
}

export function UnifiedAppointmentBooking() {
  const { currentClinic } = useAuth();
  const [loading, setLoading] = useState(false);
  const [conflicts, setConflicts] = useState<any>(null);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  
  const [formData, setFormData] = useState<UnifiedBookingFormData>({
    clientType: 'registered',
    clientData: {
      firstName: '',
      lastName: '',
      phoneNumber: '',
      email: ''
    },
    petData: {
      petName: '',
      species: '',
      breed: '',
      gender: 'unknown',
      age: 0,
      weight: 0
    },
    appointmentData: {
      appointmentDate: '',
      appointmentTime: '',
      categories: [],
      notes: '',
      priority: 'normal',
      estimatedDuration: 30
    }
  });

  // Check for conflicts when date/time changes
  useEffect(() => {
    if (formData.appointmentData.appointmentDate && formData.appointmentData.appointmentTime) {
      checkConflicts();
    }
  }, [formData.appointmentData.appointmentDate, formData.appointmentData.appointmentTime]);

  const checkConflicts = async () => {
    if (!formData.appointmentData.appointmentDate || !formData.appointmentData.appointmentTime) return;
    
    setCheckingConflicts(true);
    try {
      const response = await api.get('/appointments/conflicts', {
        params: {
          date: formData.appointmentData.appointmentDate,
          time: formData.appointmentData.appointmentTime,
          duration: formData.appointmentData.estimatedDuration
        }
      });
      
      setConflicts(response.data);
    } catch (error) {
      console.error('Error checking conflicts:', error);
    } finally {
      setCheckingConflicts(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentClinic) {
      toast({
        title: "Error",
        description: "Please select a clinic first",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const response = await api.post('/appointments/book', formData);
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Appointment booked successfully!",
          variant: "default"
        });
        
        // Reset form
        setFormData({
          clientType: 'registered',
          clientData: {
            firstName: '',
            lastName: '',
            phoneNumber: '',
            email: ''
          },
          petData: {
            petName: '',
            species: '',
            breed: '',
            gender: 'unknown',
            age: 0,
            weight: 0
          },
          appointmentData: {
            appointmentDate: '',
            appointmentTime: '',
            categories: [],
            notes: '',
            priority: 'normal',
            estimatedDuration: 30
          }
        });
        setConflicts(null);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to book appointment",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (section: keyof UnifiedBookingFormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Book Appointment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Client Type Selection */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Client Type</Label>
              <RadioGroup
                value={formData.clientType}
                onValueChange={(value: 'registered' | 'walk-in') => 
                  setFormData(prev => ({ ...prev, clientType: value }))
                }
                className="flex gap-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="registered" id="registered" />
                  <Label htmlFor="registered">Registered Client</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="walk-in" id="walk-in" />
                  <Label htmlFor="walk-in">Walk-in Client</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Client Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <User className="h-4 w-4" />
                  Client Information
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.clientData.firstName}
                    onChange={(e) => updateFormData('clientData', 'firstName', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.clientData.lastName}
                    onChange={(e) => updateFormData('clientData', 'lastName', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="phoneNumber">Phone Number *</Label>
                  <Input
                    id="phoneNumber"
                    value={formData.clientData.phoneNumber}
                    onChange={(e) => updateFormData('clientData', 'phoneNumber', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.clientData.email}
                    onChange={(e) => updateFormData('clientData', 'email', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Pet Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <PawPrint className="h-4 w-4" />
                  Pet Information
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="petName">Pet Name *</Label>
                  <Input
                    id="petName"
                    value={formData.petData.petName}
                    onChange={(e) => updateFormData('petData', 'petName', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="species">Species *</Label>
                  <Select
                    value={formData.petData.species}
                    onValueChange={(value) => updateFormData('petData', 'species', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dog">Dog</SelectItem>
                      <SelectItem value="cat">Cat</SelectItem>
                      <SelectItem value="bird">Bird</SelectItem>
                      <SelectItem value="rabbit">Rabbit</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="breed">Breed</Label>
                  <Input
                    id="breed"
                    value={formData.petData.breed}
                    onChange={(e) => updateFormData('petData', 'breed', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="gender">Gender</Label>
                  <Select
                    value={formData.petData.gender}
                    onValueChange={(value) => updateFormData('petData', 'gender', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="unknown">Unknown</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Appointment Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Clock className="h-4 w-4" />
                  Appointment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="appointmentDate">Date *</Label>
                    <Input
                      id="appointmentDate"
                      type="date"
                      value={formData.appointmentData.appointmentDate}
                      onChange={(e) => updateFormData('appointmentData', 'appointmentDate', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="appointmentTime">Time *</Label>
                    <Input
                      id="appointmentTime"
                      type="time"
                      value={formData.appointmentData.appointmentTime}
                      onChange={(e) => updateFormData('appointmentData', 'appointmentTime', e.target.value)}
                      required
                    />
                  </div>
                </div>

                {/* Conflict Detection */}
                {checkingConflicts && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>Checking for scheduling conflicts...</AlertDescription>
                  </Alert>
                )}

                {conflicts && (
                  <Alert variant={conflicts.hasConflicts ? "destructive" : "default"}>
                    {conflicts.hasConflicts ? (
                      <AlertTriangle className="h-4 w-4" />
                    ) : (
                      <CheckCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>
                      {conflicts.hasConflicts 
                        ? `Scheduling conflicts detected. ${conflicts.suggestions.length} alternative times available.`
                        : "No scheduling conflicts detected. Time slot is available."
                      }
                    </AlertDescription>
                  </Alert>
                )}

                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <Select
                    value={formData.appointmentData.priority}
                    onValueChange={(value: any) => updateFormData('appointmentData', 'priority', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="notes">Notes / Symptoms</Label>
                  <Textarea
                    id="notes"
                    value={formData.appointmentData.notes}
                    onChange={(e) => updateFormData('appointmentData', 'notes', e.target.value)}
                    placeholder="Describe symptoms, reason for visit, or any special requirements..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className="flex justify-end gap-4">
              <Button type="button" variant="outline">
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={loading || checkingConflicts}
                className="min-w-[120px]"
              >
                {loading ? "Booking..." : "Book Appointment"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
