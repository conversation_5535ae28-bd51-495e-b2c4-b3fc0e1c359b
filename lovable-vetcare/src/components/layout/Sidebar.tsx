
import React, { useState, useEffect, useRef } from "react";
import {
  Building2, Home, Users, Calendar, ClipboardList, Package2, PawPrint,
  ChevronDown, Shield, Stethoscope, Syringe, Scissors, TestTube, Cat, Dog,
  DollarSign, BarChart2, Bell, FileText, Pill, Truck, Settings, Clipboard,
  MessageCircle, CalendarClock, Activity, HeartPulse, Microscope, Thermometer, Dna
} from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";

// Background image options
const bgImages = [
  // "/pets/cat.jpg",
  "/pets/dog.jpg",
  // "/pets/horse.jpg",
  // "/pets/pig.jpg",
  // "/pets/goat.jpg"
];

const navigation = [
  { name: "Dashboard", to: "/dashboard", icon: Home, emoji: "🏠" },

  // Clients & Pets Section
  {
    name: "Clients & Pets",
    to: "/clients",
    icon: Users,
    emoji: "👩‍⚕️",
    items: [
      // Clients subsection
      { name: "All Clients", to: "/clients", icon: Users, emoji: "👥" },
      { name: "Client Groups", to: "/client-groups", icon: Users, emoji: "👪" },

      // Pets subsection
      { name: "All Pets", to: "/pets", icon: PawPrint, emoji: "🐾" },
      { name: "Species", to: "/species", icon: Cat, emoji: "🐱" },
      { name: "Breeds", to: "/breeds", icon: Dna, emoji: "🧬" },
      { name: "Medical History", to: "/pets/medical-history", icon: ClipboardList, emoji: "📋" },
    ],
  },

  // Appointments Section (standalone)
  { name: "Appointments", to: "/appointments", icon: Calendar, emoji: "📅" },

  // Treatments / Medical Records Section
  {
    name: "Treatments / Medical Records",
    to: "/records",
    icon: Stethoscope,
    emoji: "💊",
    items: [
      { name: "Ongoing Treatments", to: "/records/ongoing", icon: Activity, emoji: "🏥" },
      { name: "Past Treatments", to: "/records/past", icon: ClipboardList, emoji: "📜" },
      { name: "Add Treatment Record", to: "/records/add", icon: FileText, emoji: "➕" },
      { name: "Vaccinations", to: "/records/vaccinations", icon: Syringe, emoji: "💉" },
      { name: "Prescriptions", to: "/records/prescriptions", icon: Pill, emoji: "💊" },
    ],
  },

  // Inventory Section
  {
    name: "Inventory",
    to: "/inventory",
    icon: Package2,
    emoji: "📦",
    items: [
      { name: "All Items", to: "/inventory", icon: Package2, emoji: "📋" },
      { name: "Add New Item", to: "/inventory/add", icon: Package2, emoji: "➕" },
      { name: "Categories", to: "/inventory/categories", icon: Clipboard, emoji: "🗂️" },
      { name: "Stock Alerts", to: "/inventory/alerts", icon: Bell, emoji: "🔔" },
      { name: "Suppliers", to: "/inventory/suppliers", icon: Truck, emoji: "🚚" },
    ],
  },

  // Practice Management Section
  {
    name: "Practice Management",
    to: "/admin",
    icon: Building2,
    emoji: "🏢",
    items: [
      { name: "Clinic Management", to: "/admin/clinics", icon: Building2, emoji: "🏥" },
      { name: "Staff Management", to: "/admin/staff", icon: Users, emoji: "👨‍⚕️" },
      { name: "Roles & Permissions", to: "/admin/roles", icon: Shield, emoji: "🔒" },
      { name: "Admin Settings", to: "/admin/settings", icon: Settings, emoji: "⚙️" },
    ],
  },

  // Reports Section
  {
    name: "Reports",
    to: "/reports",
    icon: BarChart2,
    emoji: "📊",
    items: [
      { name: "Financial Reports", to: "/reports/financial", icon: DollarSign, emoji: "💰" },
      { name: "Appointment Reports", to: "/reports/appointments", icon: Calendar, emoji: "📅" },
      { name: "Inventory Reports", to: "/reports/inventory", icon: Package2, emoji: "📦" },
      { name: "Client Reports", to: "/reports/clients", icon: Users, emoji: "👥" },
      { name: "Staff Performance", to: "/reports/staff", icon: Activity, emoji: "📈" },
    ],
  },

  // System Settings
  { name: "Settings", to: "/settings", icon: Settings, emoji: "⚙️" },
];

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export const Sidebar = ({ isOpen, onClose }: SidebarProps) => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [currentBgIndex, setCurrentBgIndex] = useState(0);

  // Create refs and hover states for all navigation items upfront
  const hoverRefs = useRef<{ [key: string]: React.RefObject<HTMLElement> }>({});
  const [hoveredItems, setHoveredItems] = useState<{ [key: string]: boolean }>({});

  // Initialize refs for all navigation items
  useEffect(() => {
    navigation.forEach((item) => {
      if (!hoverRefs.current[item.name]) {
        hoverRefs.current[item.name] = React.createRef<HTMLElement>();
      }
    });
  }, []);

  // Set up hover listeners for all navigation items
  useEffect(() => {
    const currentRefs = hoverRefs.current;
    const listeners: { [key: string]: { enter: () => void; leave: () => void } } = {};

    Object.entries(currentRefs).forEach(([name, ref]) => {
      const element = ref.current;
      if (!element) return;

      const handleMouseEnter = () => {
        setHoveredItems(prev => ({ ...prev, [name]: true }));
      };

      const handleMouseLeave = () => {
        setHoveredItems(prev => ({ ...prev, [name]: false }));
      };

      element.addEventListener('mouseenter', handleMouseEnter);
      element.addEventListener('mouseleave', handleMouseLeave);

      listeners[name] = { enter: handleMouseEnter, leave: handleMouseLeave };
    });

    return () => {
      Object.entries(currentRefs).forEach(([name, ref]) => {
        const element = ref.current;
        if (!element || !listeners[name]) return;

        element.removeEventListener('mouseenter', listeners[name].enter);
        element.removeEventListener('mouseleave', listeners[name].leave);
      });
    };
  }, []);

  // Background image rotation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBgIndex((prev) => (prev + 1) % bgImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Initialize expanded sections based on current route
  useEffect(() => {
    const currentPath = location.pathname;
    const sectionsToExpand = navigation
      .filter(item => item.items && item.items.some(subItem => currentPath.startsWith(subItem.to)))
      .map(item => item.name);

    if (sectionsToExpand.length > 0) {
      setExpandedSections(sectionsToExpand);
    }
  }, [location.pathname]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('sidebar');
      if (sidebar && !sidebar.contains(event.target as Node)) {
        if (isMobile) {
          onClose();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, onClose]);

  const handleLinkClick = () => {
    // Close any expanded sections when clicking a direct link
    setExpandedSections([]);
    if (isMobile) {
      onClose();
    }
  };

  const toggleSection = (sectionName: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionName)
        ? prev.filter(item => item !== sectionName)
        : [...prev, sectionName]
    );
  };

  const isSubRouteActive = (items: { to: string }[]) => {
    return items.some(item => location.pathname.startsWith(item.to));
  };

  // For responsive design adjustments
  useEffect(() => {
    const handleResize = () => {
      // We can add responsive design logic here if needed in the future
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // For collapsed sidebar, we need to show a mini version
  if (!isOpen && !isMobile) {
    return (
      <aside
        id="sidebar"
        className="fixed left-0 top-0 h-screen bg-background dark:bg-[#0A0A0A] backdrop-blur-md border-r dark:border-gray-800 pt-20 transition-all duration-300 z-40 overflow-hidden w-16"
        style={{
          // backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(${bgImages[currentBgIndex]})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <nav className="px-2 py-4 h-full overflow-y-auto">
          <ul className="space-y-4">
            {navigation.map((item) => {
              // Get the hover state for this item
              const isHovered = hoveredItems[item.name] || false;

              return (
                <li
                  key={item.name}
                  ref={hoverRefs.current[item.name] as React.RefObject<HTMLLIElement>}
                  className="relative"
                >
                  {item.items ? (
                    <div className="contents">
                      <button
                        onClick={() => toggleSection(item.name)}
                        className={`w-full flex items-center justify-center p-3 rounded-lg transition-colors
                          ${isSubRouteActive(item.items) || expandedSections.includes(item.name)
                            ? "bg-primary/10 text-primary dark:bg-primary/20"
                            : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                          }`}
                      >
                        <div className="relative">
                          <item.icon size={24} />
                          <span className="absolute -top-2 -right-2 text-xs">{item.emoji}</span>
                        </div>
                      </button>

                      {/* Dropdown menu - shows on hover or click */}
                      {(isHovered || expandedSections.includes(item.name)) && (
                        <div
                          className="absolute left-16 top-0 bg-background dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg shadow-lg p-2 min-w-48 z-50"
                        >
                          <div className="p-2 font-semibold border-b border-gray-200 dark:border-gray-800 mb-2 flex justify-between items-center">
                            <span>{item.name}</span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleSection(item.name);
                              }}
                              className="text-gray-500 hover:text-gray-700"
                            >
                              <ChevronDown size={16} className="rotate-180" />
                            </button>
                          </div>
                          <ul className="space-y-1">
                            {item.items.map((subItem) => (
                              <li key={subItem.name}>
                                <NavLink
                                  to={subItem.to}
                                  onClick={handleLinkClick}
                                  className={({ isActive }) =>
                                    `flex items-center justify-between px-4 py-2 rounded-lg transition-colors ${
                                      isActive
                                        ? "bg-primary/10 text-primary dark:bg-primary/20"
                                        : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                                    }`
                                  }
                                >
                                  <div className="flex items-center gap-2">
                                    <subItem.icon size={18} />
                                    <span className="text-sm">{subItem.name}</span>
                                  </div>
                                  <span className="text-xs">{subItem.emoji}</span>
                                </NavLink>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ) : (
                    <NavLink
                      to={item.to}
                      onClick={handleLinkClick}
                      className={({ isActive }) =>
                        `flex items-center justify-center p-3 rounded-lg transition-colors ${
                          isActive
                            ? "bg-primary/10 text-primary dark:bg-primary/20"
                            : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                        }`
                      }
                    >
                      <div className="relative">
                        <item.icon size={24} />
                        <span className="absolute -top-2 -right-2 text-xs">{item.emoji}</span>
                      </div>
                    </NavLink>
                  )}
                </li>
              );
            })}
          </ul>
        </nav>
      </aside>
    );
  }

  if (!isOpen) return null;

  return (
    <aside
      id="sidebar"
      className={`fixed left-0 top-0 h-screen bg-background dark:bg-[#0A0A0A] backdrop-blur-md border-r dark:border-gray-800 pt-20 transition-all duration-300 z-40 overflow-hidden
        ${isMobile ? "w-full sm:w-64" : "w-64"}
        ${isOpen ? "translate-x-0" : "-translate-x-full"}`}
      style={{
        // backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(${bgImages[currentBgIndex]})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <nav className="px-4 py-4 h-full overflow-y-auto">
        <ul className="space-y-1">
          {/* Dashboard */}
          <li>
            <NavLink
              to="/dashboard"
              onClick={handleLinkClick}
              className={({ isActive }) =>
                `flex items-center justify-between px-4 py-3 rounded-lg transition-colors ${
                  isActive
                    ? "bg-primary/10 text-primary dark:bg-primary/20"
                    : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                }`
              }
            >
              <div className="flex items-center gap-2">
                <Home size={20} />
                <span className="text-sm font-semibold">Dashboard</span>
              </div>
              <span className="text-lg">🏠</span>
            </NavLink>
          </li>

          {/* Render each section with dividers */}
          {navigation.slice(1).map((item, index) => {
            // Get the hover state for this item
            const isHovered = hoveredItems[item.name] || false;

            return (
              <div key={item.name} className="contents">
                {index > 0 && (
                  <li className="my-3">
                    <div className="h-px bg-gray-200 dark:bg-gray-800 w-full opacity-50"></div>
                  </li>
                )}
                <li>
                  {item.items ? (
                    <div className="mb-1">
                      <div
                        ref={hoverRefs.current[item.name] as React.RefObject<HTMLDivElement>}
                        className="relative"
                      >
                        <button
                          onClick={() => toggleSection(item.name)}
                          className={`w-full flex items-center justify-between px-4 py-3 rounded-lg transition-colors
                            ${isSubRouteActive(item.items) || expandedSections.includes(item.name)
                              ? "bg-primary/10 text-primary dark:bg-primary/20"
                              : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                            }`}
                        >
                          <div className="flex items-center space-x-3">
                            <item.icon size={20} />
                            <span className="text-sm font-semibold">{item.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{item.emoji}</span>
                            <ChevronDown
                              size={16}
                              className={`transition-transform ${
                                expandedSections.includes(item.name) ? "rotate-180" : ""
                              }`}
                            />
                          </div>
                        </button>

                        {/* Desktop hover dropdown */}
                        {!expandedSections.includes(item.name) && isHovered && (
                          <div
                            className="absolute left-full top-0 bg-background dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg shadow-lg p-2 min-w-48 z-50 ml-2"
                          >
                            <div className="p-2 font-semibold border-b border-gray-200 dark:border-gray-800 mb-2 flex justify-between items-center">
                              <span>{item.name}</span>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleSection(item.name);
                                }}
                                className="text-gray-500 hover:text-gray-700"
                              >
                                <ChevronDown size={16} className="rotate-180" />
                              </button>
                            </div>
                            <ul className="space-y-1">
                              {item.items.map((subItem) => (
                                <li key={subItem.name}>
                                  <NavLink
                                    to={subItem.to}
                                    onClick={handleLinkClick}
                                    className={({ isActive }) =>
                                      `flex items-center justify-between px-4 py-2 rounded-lg transition-colors ${
                                        isActive
                                          ? "bg-primary/10 text-primary dark:bg-primary/20"
                                          : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                                      }`
                                    }
                                  >
                                    <div className="flex items-center gap-2">
                                      <subItem.icon size={18} />
                                      <span className="text-sm">{subItem.name}</span>
                                    </div>
                                    <span className="text-xs">{subItem.emoji}</span>
                                  </NavLink>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>

                      {/* Click dropdown (for all devices) */}
                      <ul
                        className={`mt-1 space-y-1 transition-all duration-200 ${
                          expandedSections.includes(item.name)
                            ? "max-h-96 opacity-100"
                            : "max-h-0 opacity-0 overflow-hidden"
                        }`}
                      >
                        {item.items.map((subItem) => (
                          <li key={subItem.name}>
                            <NavLink
                              to={subItem.to}
                              onClick={handleLinkClick}
                              className={({ isActive }) =>
                                `flex items-center justify-between px-8 py-2 rounded-lg transition-colors ${
                                  isActive
                                    ? "bg-primary/10 text-primary dark:bg-primary/20"
                                    : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                                }`
                              }
                            >
                              <div className="flex items-center gap-2">
                                <subItem.icon size={18} />
                                <span className="text-sm">{subItem.name}</span>
                              </div>
                              <span className="text-xs">{subItem.emoji}</span>
                            </NavLink>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ) : (
                    <NavLink
                      to={item.to}
                      onClick={handleLinkClick}
                      className={({ isActive }) =>
                        `flex items-center justify-between px-4 py-3 rounded-lg transition-colors ${
                          isActive
                            ? "bg-primary/10 text-primary dark:bg-primary/20"
                            : "text-foreground hover:bg-accent/10 dark:hover:bg-accent/20"
                        }`
                      }
                    >
                      <div className="flex items-center gap-2">
                        <item.icon size={20} />
                        <span className="text-sm font-semibold">{item.name}</span>
                      </div>
                      <span className="text-lg">{item.emoji}</span>
                    </NavLink>
                  )}
                </li>
              </div>
            );
          })}
        </ul>
      </nav>
    </aside>
  );
};
