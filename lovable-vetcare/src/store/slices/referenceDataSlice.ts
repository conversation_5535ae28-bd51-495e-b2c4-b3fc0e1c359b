/**
 * Reference Data Store Slice
 * 
 * Centralized store for caching frequently accessed reference data
 * like species, breeds, staff, roles, appointment categories, etc.
 */

import { StateCreator } from 'zustand';
import { Species, Breed, Staff, Role, AppointmentCategory, Client } from '@/store/types';
import { 
  getSpecies, 
  getBreeds, 
  getBreedsBySpecies, 
  getAllStaff, 
  getRoles, 
  getAppointmentCategories,
  getClients 
} from '@/services/api';

export interface ReferenceDataState {
  // Data
  species: Species[];
  breeds: Breed[];
  breedsBySpecies: Record<string, Breed[]>;
  staff: Staff[];
  roles: Role[];
  appointmentCategories: AppointmentCategory[];
  clients: Client[];
  
  // Loading states
  isLoadingSpecies: boolean;
  isLoadingBreeds: boolean;
  isLoadingStaff: boolean;
  isLoadingRoles: boolean;
  isLoadingAppointmentCategories: boolean;
  isLoadingClients: boolean;
  
  // Cache timestamps
  speciesLastFetched: number | null;
  breedsLastFetched: number | null;
  staffLastFetched: number | null;
  rolesLastFetched: number | null;
  appointmentCategoriesLastFetched: number | null;
  clientsLastFetched: number | null;
  
  // Cache TTL (Time To Live) in milliseconds
  cacheTTL: number;
}

export interface ReferenceDataActions {
  // Fetch actions
  fetchSpecies: (force?: boolean) => Promise<void>;
  fetchBreeds: (force?: boolean) => Promise<void>;
  fetchBreedsBySpecies: (speciesId: string, force?: boolean) => Promise<void>;
  fetchStaff: (force?: boolean) => Promise<void>;
  fetchRoles: (force?: boolean) => Promise<void>;
  fetchAppointmentCategories: (force?: boolean) => Promise<void>;
  fetchClients: (force?: boolean) => Promise<void>;
  
  // Utility actions
  clearCache: () => void;
  clearSpecificCache: (dataType: keyof ReferenceDataState) => void;
  isDataStale: (lastFetched: number | null) => boolean;
  
  // Getters with automatic fetching
  getSpeciesData: () => Promise<Species[]>;
  getBreedsData: () => Promise<Breed[]>;
  getBreedsBySpeciesData: (speciesId: string) => Promise<Breed[]>;
  getStaffData: () => Promise<Staff[]>;
  getRolesData: () => Promise<Role[]>;
  getAppointmentCategoriesData: () => Promise<AppointmentCategory[]>;
  getClientsData: () => Promise<Client[]>;
}

export type ReferenceDataSlice = ReferenceDataState & ReferenceDataActions;

const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export const createReferenceDataSlice: StateCreator<
  ReferenceDataSlice,
  [],
  [],
  ReferenceDataSlice
> = (set, get) => ({
  // Initial state
  species: [],
  breeds: [],
  breedsBySpecies: {},
  staff: [],
  roles: [],
  appointmentCategories: [],
  clients: [],
  
  isLoadingSpecies: false,
  isLoadingBreeds: false,
  isLoadingStaff: false,
  isLoadingRoles: false,
  isLoadingAppointmentCategories: false,
  isLoadingClients: false,
  
  speciesLastFetched: null,
  breedsLastFetched: null,
  staffLastFetched: null,
  rolesLastFetched: null,
  appointmentCategoriesLastFetched: null,
  clientsLastFetched: null,
  
  cacheTTL: CACHE_TTL,
  
  // Utility function to check if data is stale
  isDataStale: (lastFetched: number | null) => {
    if (!lastFetched) return true;
    return Date.now() - lastFetched > CACHE_TTL;
  },
  
  // Fetch species
  fetchSpecies: async (force = false) => {
    const state = get();
    if (!force && !state.isDataStale(state.speciesLastFetched)) {
      return; // Data is still fresh
    }
    
    if (state.isLoadingSpecies) return; // Already loading
    
    set({ isLoadingSpecies: true });
    
    try {
      const response = await getSpecies({ limit: 100 });
      if (response.success && response.data?.data) {
        set({
          species: response.data.data,
          speciesLastFetched: Date.now(),
          isLoadingSpecies: false
        });
      }
    } catch (error) {
      console.error('Failed to fetch species:', error);
      set({ isLoadingSpecies: false });
    }
  },
  
  // Fetch breeds
  fetchBreeds: async (force = false) => {
    const state = get();
    if (!force && !state.isDataStale(state.breedsLastFetched)) {
      return;
    }
    
    if (state.isLoadingBreeds) return;
    
    set({ isLoadingBreeds: true });
    
    try {
      const response = await getBreeds({ limit: 200 });
      if (response.success && response.data?.data) {
        set({
          breeds: response.data.data,
          breedsLastFetched: Date.now(),
          isLoadingBreeds: false
        });
      }
    } catch (error) {
      console.error('Failed to fetch breeds:', error);
      set({ isLoadingBreeds: false });
    }
  },
  
  // Fetch breeds by species
  fetchBreedsBySpecies: async (speciesId: string, force = false) => {
    const state = get();
    const cached = state.breedsBySpecies[speciesId];
    
    if (!force && cached && cached.length > 0) {
      return; // Already have data for this species
    }
    
    try {
      const response = await getBreedsBySpecies({ speciesId, limit: 100 });
      if (response.success && response.data?.data) {
        set({
          breedsBySpecies: {
            ...state.breedsBySpecies,
            [speciesId]: response.data.data
          }
        });
      }
    } catch (error) {
      console.error('Failed to fetch breeds by species:', error);
    }
  },
  
  // Fetch staff
  fetchStaff: async (force = false) => {
    const state = get();
    if (!force && !state.isDataStale(state.staffLastFetched)) {
      return;
    }
    
    if (state.isLoadingStaff) return;
    
    set({ isLoadingStaff: true });
    
    try {
      const response = await getAllStaff();
      if (response.success && response.data?.data) {
        set({
          staff: response.data.data,
          staffLastFetched: Date.now(),
          isLoadingStaff: false
        });
      }
    } catch (error) {
      console.error('Failed to fetch staff:', error);
      set({ isLoadingStaff: false });
    }
  },
  
  // Fetch roles
  fetchRoles: async (force = false) => {
    const state = get();
    if (!force && !state.isDataStale(state.rolesLastFetched)) {
      return;
    }
    
    if (state.isLoadingRoles) return;
    
    set({ isLoadingRoles: true });
    
    try {
      const response = await getRoles();
      if (response.success && response.data?.data) {
        set({
          roles: response.data.data,
          rolesLastFetched: Date.now(),
          isLoadingRoles: false
        });
      }
    } catch (error) {
      console.error('Failed to fetch roles:', error);
      set({ isLoadingRoles: false });
    }
  },
  
  // Fetch appointment categories
  fetchAppointmentCategories: async (force = false) => {
    const state = get();
    if (!force && !state.isDataStale(state.appointmentCategoriesLastFetched)) {
      return;
    }
    
    if (state.isLoadingAppointmentCategories) return;
    
    set({ isLoadingAppointmentCategories: true });
    
    try {
      const response = await getAppointmentCategories();
      if (response.success && response.data) {
        set({
          appointmentCategories: response.data,
          appointmentCategoriesLastFetched: Date.now(),
          isLoadingAppointmentCategories: false
        });
      }
    } catch (error) {
      console.error('Failed to fetch appointment categories:', error);
      set({ isLoadingAppointmentCategories: false });
    }
  },
  
  // Fetch clients
  fetchClients: async (force = false) => {
    const state = get();
    if (!force && !state.isDataStale(state.clientsLastFetched)) {
      return;
    }
    
    if (state.isLoadingClients) return;
    
    set({ isLoadingClients: true });
    
    try {
      const response = await getClients({ limit: 200 });
      if (response.success && response.data?.data) {
        set({
          clients: response.data.data,
          clientsLastFetched: Date.now(),
          isLoadingClients: false
        });
      }
    } catch (error) {
      console.error('Failed to fetch clients:', error);
      set({ isLoadingClients: false });
    }
  },
  
  // Clear all cache
  clearCache: () => {
    set({
      species: [],
      breeds: [],
      breedsBySpecies: {},
      staff: [],
      roles: [],
      appointmentCategories: [],
      clients: [],
      speciesLastFetched: null,
      breedsLastFetched: null,
      staffLastFetched: null,
      rolesLastFetched: null,
      appointmentCategoriesLastFetched: null,
      clientsLastFetched: null,
    });
  },
  
  // Clear specific cache
  clearSpecificCache: (dataType: keyof ReferenceDataState) => {
    const updates: Partial<ReferenceDataState> = {};
    
    switch (dataType) {
      case 'species':
        updates.species = [];
        updates.speciesLastFetched = null;
        break;
      case 'breeds':
        updates.breeds = [];
        updates.breedsBySpecies = {};
        updates.breedsLastFetched = null;
        break;
      case 'staff':
        updates.staff = [];
        updates.staffLastFetched = null;
        break;
      case 'roles':
        updates.roles = [];
        updates.rolesLastFetched = null;
        break;
      case 'appointmentCategories':
        updates.appointmentCategories = [];
        updates.appointmentCategoriesLastFetched = null;
        break;
      case 'clients':
        updates.clients = [];
        updates.clientsLastFetched = null;
        break;
    }
    
    set(updates);
  },
  
  // Getter methods with automatic fetching
  getSpeciesData: async () => {
    const state = get();
    await state.fetchSpecies();
    return get().species;
  },
  
  getBreedsData: async () => {
    const state = get();
    await state.fetchBreeds();
    return get().breeds;
  },
  
  getBreedsBySpeciesData: async (speciesId: string) => {
    const state = get();
    await state.fetchBreedsBySpecies(speciesId);
    return get().breedsBySpecies[speciesId] || [];
  },
  
  getStaffData: async () => {
    const state = get();
    await state.fetchStaff();
    return get().staff;
  },
  
  getRolesData: async () => {
    const state = get();
    await state.fetchRoles();
    return get().roles;
  },
  
  getAppointmentCategoriesData: async () => {
    const state = get();
    await state.fetchAppointmentCategories();
    return get().appointmentCategories;
  },
  
  getClientsData: async () => {
    const state = get();
    await state.fetchClients();
    return get().clients;
  },
});
