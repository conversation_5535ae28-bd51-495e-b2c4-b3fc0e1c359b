import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { X, Plus, Minus, Receipt, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { completeAppointment, generateReceipt } from '@/services/healthRecords';
import { Appointment } from '@/store/types';

const completionSchema = z.object({
  diagnosis: z.string().min(3, "Diagnosis is required"),
  treatment: z.string().min(3, "Treatment is required"),
  followUpInstructions: z.string().optional(),
  followUpDate: z.string().optional(),
  notes: z.string().optional(),
  vitalSigns: z.object({
    temperature: z.number().optional(),
    heartRate: z.number().optional(),
    respiratoryRate: z.number().optional(),
    weight: z.number().optional(),
    bloodPressure: z.string().optional(),
  }).optional(),
});

type CompletionFormValues = z.infer<typeof completionSchema>;

interface CompleteAppointmentModalProps {
  appointment: Appointment;
  isOpen: boolean;
  onClose: () => void;
  onCompleted: (healthRecord: any) => void;
}

const CompleteAppointmentModal: React.FC<CompleteAppointmentModalProps> = ({
  appointment,
  isOpen,
  onClose,
  onCompleted
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [medications, setMedications] = useState<Array<{
    name: string;
    dosage: string;
    frequency: string;
    duration: string;
    notes: string;
  }>>([]);
  const [labResults, setLabResults] = useState<Array<{
    testName: string;
    result: string;
    normalRange: string;
    interpretation: string;
  }>>([]);

  const form = useForm<CompletionFormValues>({
    resolver: zodResolver(completionSchema),
    defaultValues: {
      diagnosis: '',
      treatment: '',
      followUpInstructions: '',
      notes: '',
      vitalSigns: {
        temperature: undefined,
        heartRate: undefined,
        respiratoryRate: undefined,
        weight: undefined,
        bloodPressure: '',
      }
    }
  });

  const completeMutation = useMutation({
    mutationFn: (data: any) => completeAppointment(appointment.appointmentId!, data),
    onSuccess: (response) => {
      toast({
        title: "Appointment Completed",
        description: "Health record created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointment', appointment.appointmentId] });
      onCompleted(response.data.healthRecord);
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to complete appointment",
        variant: "destructive",
      });
    }
  });

  const onSubmit = (data: CompletionFormValues) => {
    const completionData = {
      ...data,
      medications: medications.filter(med => med.name && med.dosage),
      labResults: labResults.filter(lab => lab.testName && lab.result),
      followUpDate: data.followUpDate ? new Date(data.followUpDate).toISOString() : undefined,
    };

    completeMutation.mutate(completionData);
  };

  const addMedication = () => {
    setMedications([...medications, { name: '', dosage: '', frequency: '', duration: '', notes: '' }]);
  };

  const removeMedication = (index: number) => {
    setMedications(medications.filter((_, i) => i !== index));
  };

  const updateMedication = (index: number, field: string, value: string) => {
    const updated = [...medications];
    updated[index] = { ...updated[index], [field]: value };
    setMedications(updated);
  };

  const addLabResult = () => {
    setLabResults([...labResults, { testName: '', result: '', normalRange: '', interpretation: '' }]);
  };

  const removeLabResult = (index: number) => {
    setLabResults(labResults.filter((_, i) => i !== index));
  };

  const updateLabResult = (index: number, field: string, value: string) => {
    const updated = [...labResults];
    updated[index] = { ...updated[index], [field]: value };
    setLabResults(updated);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold">Complete Appointment</h2>
            <p className="text-gray-600">
              {appointment.petName} - {(() => {
                try {
                  const dateValue = appointment.dateTime || (appointment as any).appointmentDate;
                  if (!dateValue) return "Invalid Date";
                  const date = new Date(dateValue);
                  if (isNaN(date.getTime())) return "Invalid Date";
                  return format(date, "MMM d, yyyy 'at' h:mm a");
                } catch (error) {
                  return "Invalid Date";
                }
              })()}
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Appointment Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Appointment Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Patient:</span> {
                    appointment.petName ||
                    appointment.petData?.petName ||
                    appointment.petData?.name ||
                    'Unknown Pet'
                  }
                </div>
                <div>
                  <span className="font-medium">Client:</span> {
                    appointment.clientName ||
                    (appointment.clientData ? `${appointment.clientData.firstName} ${appointment.clientData.lastName}` : '') ||
                    'Unknown Client'
                  }
                </div>
                <div>
                  <span className="font-medium">Veterinarian:</span> {
                    appointment.staffData?.name ||
                    (appointment.staffData ? `Dr. ${appointment.staffData.firstName} ${appointment.staffData.lastName}` : '') ||
                    'Not assigned'
                  }
                </div>
                <div>
                  <span className="font-medium">Type:</span>
                  <Badge variant="secondary" className="ml-2">
                    {appointment.appointmentTypes?.[0]?.name || 'General Visit'}
                  </Badge>
                </div>
                <div className="col-span-2">
                  <span className="font-medium">Reason:</span> {appointment.reason}
                </div>
                {/* After Hours Indicator */}
                {(() => {
                  try {
                    const dateValue = appointment.dateTime || (appointment as any).appointmentDate;
                    if (!dateValue) return null;
                    const appointmentTime = new Date(dateValue);
                    if (isNaN(appointmentTime.getTime())) return null;
                    const appointmentHour = appointmentTime.getHours();
                    const isAfterHours = appointmentHour < 8 || appointmentHour >= 18;

                    if (isAfterHours) {
                      return (
                        <div className="col-span-2">
                          <span className="font-medium text-orange-600">After Hours Visit:</span>
                          <span className="ml-2 text-sm text-orange-600">
                            50% surcharge will be applied
                          </span>
                        </div>
                      );
                    }
                    return null;
                  } catch (error) {
                    return null;
                  }
                  return null;
                })()}
              </div>
            </CardContent>
          </Card>

          {/* Medical Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="diagnosis">Diagnosis *</Label>
                <Textarea
                  id="diagnosis"
                  placeholder="Enter diagnosis..."
                  {...form.register('diagnosis')}
                  className="mt-1"
                />
                {form.formState.errors.diagnosis && (
                  <p className="text-red-500 text-sm mt-1">{form.formState.errors.diagnosis.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="treatment">Treatment *</Label>
                <Textarea
                  id="treatment"
                  placeholder="Enter treatment provided..."
                  {...form.register('treatment')}
                  className="mt-1"
                />
                {form.formState.errors.treatment && (
                  <p className="text-red-500 text-sm mt-1">{form.formState.errors.treatment.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="followUpInstructions">Follow-up Instructions</Label>
                <Textarea
                  id="followUpInstructions"
                  placeholder="Enter follow-up instructions..."
                  {...form.register('followUpInstructions')}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="followUpDate">Follow-up Date</Label>
                <Input
                  id="followUpDate"
                  type="date"
                  {...form.register('followUpDate')}
                  className="mt-1"
                />
              </div>
            </div>
          </div>

          {/* Vital Signs */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Vital Signs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div>
                  <Label htmlFor="temperature">Temperature (°C)</Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    placeholder="38.5"
                    {...form.register('vitalSigns.temperature', { valueAsNumber: true })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="heartRate">Heart Rate (bpm)</Label>
                  <Input
                    id="heartRate"
                    type="number"
                    placeholder="120"
                    {...form.register('vitalSigns.heartRate', { valueAsNumber: true })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="respiratoryRate">Respiratory Rate</Label>
                  <Input
                    id="respiratoryRate"
                    type="number"
                    placeholder="30"
                    {...form.register('vitalSigns.respiratoryRate', { valueAsNumber: true })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="weight">Weight (kg)</Label>
                  <Input
                    id="weight"
                    type="number"
                    step="0.1"
                    placeholder="25.5"
                    {...form.register('vitalSigns.weight', { valueAsNumber: true })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="bloodPressure">Blood Pressure</Label>
                  <Input
                    id="bloodPressure"
                    placeholder="120/80"
                    {...form.register('vitalSigns.bloodPressure')}
                    className="mt-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Notes */}
          <div>
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              placeholder="Any additional observations or notes..."
              {...form.register('notes')}
              className="mt-1"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={completeMutation.isPending}
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              {completeMutation.isPending ? "Completing..." : "Complete Visit"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CompleteAppointmentModal;
