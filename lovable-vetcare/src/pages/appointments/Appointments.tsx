
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Calendar, CalendarPlus, Filter, Search, MoreHorizontal,
  CheckCircle, XCircle, RefreshCcwIcon, Clock, User, Phone,
  Stethoscope, Loader2, Eye, Edit, PlayCircle, X, Bell
} from "lucide-react";
import { getAppointments, updateAppointment } from "@/services/appointments";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Appointment } from "@/store/types";
import { format } from "date-fns";
import LoadingPage from "@/components/common/LoadingPage";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate } from "react-router-dom";
import { DebouncedSearchInput, InputField } from "@/components/common";
import { cn } from "@/lib/utils";

const Appointments = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const { toast } = useToast();
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState({
    petName: '',
    ownerName: '',
    phone: ''
  });
  const [searchParams, setSearchParams] = useState({
    petName: '',
    ownerName: '',
    phone: ''
  });

  const handleStatusChange = async (appointmentId: string, newStatus: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show') => {
    try {
      await updateAppointment(appointmentId, { status: newStatus });
      await queryClient.invalidateQueries({ queryKey: ["appointments"] });
      toast({
        title: "Success",
        description: `Appointment status updated to ${newStatus.replace('_', ' ').charAt(0).toUpperCase() + newStatus.replace('_', ' ').slice(1)}`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update appointment status",
        variant: "destructive"
      });
    }
  };

  const handleSearch = () => {
    setSearchParams(searchQuery);
    queryClient.invalidateQueries({
      queryKey: ["appointments", currentPage, limit, searchParams]
    });
  };

  // Update the useQuery to use searchParams instead of searchQuery
  const { data: appointmentsResponse, isLoading, refetch } = useQuery({
    queryKey: ["appointments", currentPage, limit, selectedStatus, searchParams],
    queryFn: async () => {
      console.log("🔍 Fetching appointments with params:", {
        page: currentPage,
        offset: (currentPage - 1),
        limit,
        petName: searchParams.petName,
        ownerName: searchParams.ownerName,
        phone: searchParams.phone,
        status: selectedStatus !== "all" ? selectedStatus : undefined
      });

      const result = await getAppointments({
        page: currentPage,
        offset: (currentPage - 1),
        limit,
        petName: searchParams.petName,
        ownerName: searchParams.ownerName,
        phone: searchParams.phone,
        status: selectedStatus !== "all" ? selectedStatus as any : undefined
      });

      console.log("📊 Appointments response:", result);
      return result;
    },
  });

  if (isLoading) {
    return <LoadingPage />;
  }

  const appointments = appointmentsResponse?.data?.data || [];
  const pagination = appointmentsResponse?.data?.pagination;
  const totalPages = pagination?.totalPages || 1;
  const totalCount = pagination?.totalCount || 0;

  const filteredAppointments = selectedStatus === "all"
    ? appointments
    : appointments.filter((appointment: Appointment) =>
        appointment.status === selectedStatus
      );

  const getStatusBadgeClass = (status: string) => {
    const statusClasses = {
      scheduled: "bg-blue-100 text-blue-800",
      in_progress: "bg-yellow-100 text-yellow-800",
      completed: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800",
      no_show: "bg-gray-100 text-gray-800",
      default: "bg-gray-100 text-gray-800"
    };
    return statusClasses[status as keyof typeof statusClasses] || statusClasses.default;
  };

  const getAppointmentDate = (appointment: Appointment) => {
    // Use the new denormalized appointmentDate field first, then fallback to dateTime
    return (appointment as any).appointmentDate || appointment.dateTime;
  };

  const formatAppointmentDate = (appointment: Appointment) => {
    try {
      const dateValue = getAppointmentDate(appointment);
      if (!dateValue) return "Invalid Date";
      const dateObj = new Date(dateValue);
      if (isNaN(dateObj.getTime())) return "Invalid Date";
      return format(dateObj, "MMMM d, yyyy");
    } catch (error) {
      console.error("Error formatting date:", error, "Appointment:", appointment);
      return "Invalid Date";
    }
  };

  const formatAppointmentTime = (appointment: Appointment) => {
    try {
      const dateValue = getAppointmentDate(appointment);
      if (!dateValue) return "Invalid Time";
      const dateObj = new Date(dateValue);
      if (isNaN(dateObj.getTime())) return "Invalid Time";
      return format(dateObj, "h:mm a");
    } catch (error) {
      console.error("Error formatting time:", error, "Appointment:", appointment);
      return "Invalid Time";
    }
  };

  // Helper function to get pet name from denormalized data
  const getPetName = (appointment: Appointment) => {
    return (appointment as any).petName ||
           appointment.petData?.petName ||
           appointment.petData?.name ||
           'Unknown Pet';
  };

  // Helper function to get client name from denormalized data
  const getClientName = (appointment: Appointment) => {
    return (appointment as any).clientName ||
           appointment.clientName ||
           (appointment.clientData ? `${appointment.clientData.firstName} ${appointment.clientData.lastName}`.trim() : '') ||
           'Unknown Client';
  };

  const upcomingAppointments = appointments
    .filter((appointment: Appointment) => {
      if (appointment.status !== "scheduled") return false;

      // Use the helper function to get the date
      const dateValue = getAppointmentDate(appointment);
      if (!dateValue) return false;

      // Check if date is valid
      const appointmentDate = new Date(dateValue);
      if (isNaN(appointmentDate.getTime())) return false;

      // Compare dates - use start of today to include appointments later today
      const now = new Date();
      now.setHours(0, 0, 0, 0); // Start of today

      return appointmentDate >= now;
    })
    .sort((a, b) => {
      // Sort by date ascending (earliest first)
      const dateA = new Date(getAppointmentDate(a));
      const dateB = new Date(getAppointmentDate(b));
      return dateA.getTime() - dateB.getTime();
    })
    .slice(0, 3); // Show only next 3 upcoming appointments

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Appointments</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => navigate("/appointments/follow-ups")}
          >
            <Bell className="h-4 w-4" />
            Follow-ups
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => navigate("/appointments/add")}
          >
            <CalendarPlus className="h-4 w-4" />
            Add Appointment
          </Button>
        </div>
      </div>

      {/* Upcoming Appointments Cards */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Upcoming Appointments</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {upcomingAppointments.length === 0 ? (
            <Card className="col-span-full bg-muted/30">
              <CardContent className="pt-6 flex flex-col items-center justify-center h-40">
                <Calendar className="h-10 w-10 text-muted-foreground mb-2" />
                <p className="text-center text-muted-foreground">No upcoming appointments</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => navigate("/appointments/add")}
                >
                  <CalendarPlus className="h-4 w-4 mr-2" />
                  Schedule New
                </Button>
              </CardContent>
            </Card>
          ) : (
            upcomingAppointments.map((appointment: Appointment) => (
              <Card
                key={appointment.appointmentId}
                className={cn(
                  "overflow-hidden transition-all duration-300 hover:shadow-md",
                  appointment.status === "scheduled" && "border-l-4 border-l-blue-500",
                  appointment.status === "completed" && "border-l-4 border-l-green-500",
                  appointment.status === "cancelled" && "border-l-4 border-l-red-500"
                )}
              >
                <CardHeader className="pb-2 bg-muted/30">
                  <CardTitle className="text-base flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Stethoscope className="h-4 w-4 text-primary" />
                      {getPetName(appointment)}
                    </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(appointment.status)}`}>
                      {appointment.status.replace('_', ' ').charAt(0).toUpperCase() + appointment.status.replace('_', ' ').slice(1)}
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>{getClientName(appointment)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{formatAppointmentDate(appointment)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{formatAppointmentTime(appointment)}</span>
                    </div>
                    <div className="flex justify-end mt-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs"
                        onClick={() => navigate(`/appointments/${appointment.appointmentId}`)}
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* Remove the separate Filters Card and modify the All Appointments section */}
      <Card>
        <CardHeader className="pb-2 flex flex-wrap justify-between items-start gap-4">
          <CardTitle>All Appointments</CardTitle>
          <div className="flex items-center gap-3">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Search
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">Search Appointments</h4>
                    <p className="text-sm text-muted-foreground">
                      Search by pet or owner details
                    </p>
                  </div>
                  <div className="grid gap-3">
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                        <User className="h-4 w-4" />
                      </div>
                      <input
                        className="flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        placeholder="Owner name"
                        value={searchQuery.ownerName}
                        onChange={(e) => setSearchQuery(prev => ({
                          ...prev,
                          ownerName: e.target.value
                        }))}
                      />
                    </div>

                    <div className="relative">
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                        <Stethoscope className="h-4 w-4" />
                      </div>
                      <input
                        className="flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        placeholder="Pet name"
                        value={searchQuery.petName}
                        onChange={(e) => setSearchQuery(prev => ({
                          ...prev,
                          petName: e.target.value
                        }))}
                      />
                    </div>

                    <div className="relative">
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                        <Phone className="h-4 w-4" />
                      </div>
                      <input
                        className="flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        placeholder="Phone number"
                        value={searchQuery.phone}
                        onChange={(e) => setSearchQuery(prev => ({
                          ...prev,
                          phone: e.target.value
                        }))}
                      />
                    </div>

                    <Button
                      className="w-full mt-1"
                      onClick={() => {
                        setIsSearching(true);
                        handleSearch();
                        setIsSearching(false);
                      }}
                      disabled={isSearching}
                    >
                      {isSearching ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Searching...
                        </>
                      ) : (
                        <>
                          <Search className="mr-2 h-4 w-4" />
                          Search
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Status:</span>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-36">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no_show">No Show</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="py-3 px-4 text-left font-medium text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Stethoscope className="h-4 w-4" />
                      Pet
                    </div>
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Owner
                    </div>
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Date
                    </div>
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Time
                    </div>
                  </th>
                  <th className="py-3 px-4 text-left font-medium text-sm text-muted-foreground">Status</th>
                  <th className="py-3 px-4 text-right font-medium text-sm text-muted-foreground">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAppointments.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-8 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Calendar className="h-10 w-10 mb-2" />
                        <p>No appointments found</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-4"
                          onClick={() => navigate("/appointments/add")}
                        >
                          <CalendarPlus className="h-4 w-4 mr-2" />
                          Add New Appointment
                        </Button>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredAppointments.map((appointment: Appointment) => (
                    <tr
                      key={appointment.appointmentId}
                      className={cn(
                        "border-b transition-colors hover:bg-muted/50 cursor-pointer",
                        appointment.status === "cancelled" && "bg-red-50/30"
                      )}
                      onClick={() => navigate(`/appointments/${appointment.appointmentId}`)}
                    >
                      <td className="py-3 px-4">
                        <div className="font-medium">
                          {getPetName(appointment)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {getClientName(appointment)}
                      </td>
                      <td className="py-3 px-4">
                        {formatAppointmentDate(appointment)}
                      </td>
                      <td className="py-3 px-4">{formatAppointmentTime(appointment)}</td>
                      <td className="py-3 px-4">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(
                            appointment.status
                          )}`}
                        >
                          {appointment.status.replace('_', ' ').charAt(0).toUpperCase() +
                            appointment.status.replace('_', ' ').slice(1)}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div onClick={(e) => e.stopPropagation()}>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => navigate(`/appointments/${appointment.appointmentId}`)}>
                                <span className="flex items-center gap-2">
                                  <Eye className="h-4 w-4 text-blue-500" />
                                  View Details
                                </span>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => navigate(`/appointments/${appointment.appointmentId}/edit`)}>
                                <span className="flex items-center gap-2">
                                  <Edit className="h-4 w-4 text-orange-500" />
                                  Edit Appointment
                                </span>
                              </DropdownMenuItem>
                              {appointment.status === 'scheduled' && (
                                <DropdownMenuItem onClick={() => handleStatusChange(appointment.appointmentId?.toString() || '', 'in_progress')}>
                                  <span className="flex items-center gap-2">
                                    <PlayCircle className="h-4 w-4 text-blue-500" />
                                    Start Appointment
                                  </span>
                                </DropdownMenuItem>
                              )}
                              {(appointment.status === 'in_progress' || appointment.status === 'scheduled') && (
                                <DropdownMenuItem onClick={() => handleStatusChange(appointment.appointmentId?.toString() || '', 'completed')}>
                                  <span className="flex items-center gap-2">
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                    Mark as Completed
                                  </span>
                                </DropdownMenuItem>
                              )}
                              {appointment.status !== 'scheduled' && appointment.status !== 'cancelled' && (
                                <DropdownMenuItem onClick={() => handleStatusChange(appointment.appointmentId?.toString() || '', 'scheduled')}>
                                  <span className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-blue-500" />
                                    Mark as Scheduled
                                  </span>
                                </DropdownMenuItem>
                              )}
                              {appointment.status !== 'cancelled' && appointment.status !== 'completed' && (
                                <DropdownMenuItem onClick={() => handleStatusChange(appointment.appointmentId?.toString() || '', 'cancelled')}>
                                  <span className="flex items-center gap-2">
                                    <XCircle className="h-4 w-4 text-red-500" />
                                    Cancel Appointment
                                  </span>
                                </DropdownMenuItem>
                              )}
                              {appointment.status === 'scheduled' && (
                                <DropdownMenuItem onClick={() => handleStatusChange(appointment.appointmentId?.toString() || '', 'no_show')}>
                                  <span className="flex items-center gap-2">
                                    <X className="h-4 w-4 text-gray-500" />
                                    Mark as No Show
                                  </span>
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          <div className="flex justify-between items-center mt-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Items per page:
              </span>
              <Select
                value={String(limit)}
                onValueChange={(value) => {
                  setLimit(Number(value));
                  setCurrentPage(1); // Reset to first page when limit changes
                }}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder="10" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon" onClick={() => refetch()}>
                <RefreshCcwIcon className="h-4 w-4" />
              </Button>
            </div>

            {totalPages > 1 && (
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {Array.from({ length: totalPages }).map((_, index) => {
                    const pageNumber = index + 1;
                    // Show first page, last page, and pages around current page
                    if (
                      pageNumber === 1 ||
                      pageNumber === totalPages ||
                      (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                    ) {
                      return (
                        <PaginationItem key={pageNumber}>
                          <PaginationLink
                            isActive={pageNumber === currentPage}
                            onClick={() => setCurrentPage(pageNumber)}
                            className="cursor-pointer"
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    } else if (
                      pageNumber === currentPage - 2 ||
                      pageNumber === currentPage + 2
                    ) {
                      return (
                        <PaginationItem key={pageNumber}>
                          <PaginationEllipsis />
                        </PaginationItem>
                      );
                    }
                    return null;
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        </CardContent>
      </Card>


    </div>
  );
};

export default Appointments;
