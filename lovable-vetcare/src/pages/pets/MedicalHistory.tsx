import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, FileText, Stethoscope, Syringe, Scissors, FlaskConical, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getPetById } from '@/services/pets';
import { getAppointmentsByClient } from '@/services/appointments';

const MedicalHistory: React.FC = () => {
  const { petId } = useParams<{ petId: string }>();
  const navigate = useNavigate();

  const { data: petResponse, isLoading: petLoading } = useQuery({
    queryKey: ['pet', petId],
    queryFn: () => getPetById(petId!),
    enabled: !!petId
  });

  const { data: appointmentsResponse, isLoading: appointmentsLoading } = useQuery({
    queryKey: ['pet-appointments', petId],
    queryFn: () => getAppointmentsByClient(petId!, { petId: petId! }),
    enabled: !!petId
  });

  const pet = petResponse?.data;
  const appointments = appointmentsResponse?.data?.data || [];

  const getVisitTypeIcon = (appointmentType: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      'Vaccination': <Syringe className="h-4 w-4" />,
      'Surgery': <Scissors className="h-4 w-4" />,
      'Laboratory': <FlaskConical className="h-4 w-4" />,
      'Consultation': <Stethoscope className="h-4 w-4" />,
      'Wellness Check': <Heart className="h-4 w-4" />,
      'Emergency': <Heart className="h-4 w-4 text-red-500" />,
      'default': <FileText className="h-4 w-4" />
    };
    return iconMap[appointmentType] || iconMap.default;
  };

  const getStatusBadgeClass = (status: string) => {
    const statusClasses = {
      scheduled: "bg-blue-100 text-blue-800",
      completed: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800",
      default: "bg-gray-100 text-gray-800"
    };
    return statusClasses[status as keyof typeof statusClasses] || statusClasses.default;
  };

  const formatDate = (appointment: any) => {
    try {
      const dateValue = appointment.dateTime || appointment.appointmentDate;
      if (!dateValue) return "Invalid Date";
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return "Invalid Date";
      return format(date, "MMM d, yyyy");
    } catch (error) {
      return "Invalid Date";
    }
  };

  const formatTime = (appointment: any) => {
    try {
      const dateValue = appointment.dateTime || appointment.appointmentDate;
      if (!dateValue) return "Invalid Time";
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return "Invalid Time";
      return format(date, "h:mm a");
    } catch (error) {
      return "Invalid Time";
    }
  };

  // Group appointments by type for different tabs
  const groupedAppointments = {
    all: appointments,
    vaccinations: appointments.filter((apt: any) =>
      apt.appointmentTypes?.some((type: any) => type.name?.toLowerCase().includes('vaccination'))
    ),
    consultations: appointments.filter((apt: any) =>
      apt.appointmentTypes?.some((type: any) => type.name?.toLowerCase().includes('consultation'))
    ),
    surgeries: appointments.filter((apt: any) =>
      apt.appointmentTypes?.some((type: any) => type.name?.toLowerCase().includes('surgery'))
    ),
    laboratory: appointments.filter((apt: any) =>
      apt.appointmentTypes?.some((type: any) => type.name?.toLowerCase().includes('laboratory'))
    )
  };

  if (petLoading || appointmentsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading medical history...</p>
        </div>
      </div>
    );
  }

  if (!pet) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Pet not found</p>
          <Button onClick={() => navigate('/pets')} className="mt-4">
            Back to Pets
          </Button>
        </div>
      </div>
    );
  }

  const AppointmentCard = ({ appointment }: { appointment: any }) => (
    <Card className="mb-4 hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => navigate(`/appointments/${appointment.appointmentId}`)}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <div className="mt-1">
              {getVisitTypeIcon(appointment.appointmentTypes?.[0]?.name || 'default')}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium">
                  {appointment.appointmentTypes?.[0]?.name || 'General Visit'}
                </h4>
                <Badge className={getStatusBadgeClass(appointment.status)} variant="secondary">
                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 mb-2">{appointment.reason}</p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(appointment)}</span>
                </div>
                <span>{formatTime(appointment)}</span>
                <span>{appointment.duration} min</span>
              </div>
              {appointment.notes && (
                <p className="text-sm text-gray-600 mt-2 italic">
                  Notes: {appointment.notes}
                </p>
              )}
            </div>
          </div>
          <div className="text-right">
            {appointment.appointmentTypes?.[0]?.price && (
              <p className="text-sm font-medium">
                {appointment.appointmentTypes[0].currency} {appointment.appointmentTypes[0].price}
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/pets')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Pets
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Medical History</h1>
            <p className="text-gray-600">{pet.petName || pet.name} - {pet.species?.name}</p>
          </div>
        </div>
        <Button onClick={() => navigate('/appointments/add')}>
          Schedule New Visit
        </Button>
      </div>

      {/* Pet Summary Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Patient Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Name</label>
              <p className="font-medium">{pet.petName || pet.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Species</label>
              <p>{pet.species?.name || 'Unknown'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Breed</label>
              <p>{pet.breed?.name || 'Mixed'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Age</label>
              <p>{pet.age || 'Unknown'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Medical History Tabs */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All Visits ({groupedAppointments.all.length})</TabsTrigger>
          <TabsTrigger value="consultations">Consultations ({groupedAppointments.consultations.length})</TabsTrigger>
          <TabsTrigger value="vaccinations">Vaccinations ({groupedAppointments.vaccinations.length})</TabsTrigger>
          <TabsTrigger value="surgeries">Surgeries ({groupedAppointments.surgeries.length})</TabsTrigger>
          <TabsTrigger value="laboratory">Lab Tests ({groupedAppointments.laboratory.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="space-y-4">
            {groupedAppointments.all.length === 0 ? (
              <Card>
                <CardContent className="pt-6 flex flex-col items-center justify-center h-40">
                  <FileText className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-center text-muted-foreground">No medical history found</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => navigate('/appointments/add')}
                  >
                    Schedule First Visit
                  </Button>
                </CardContent>
              </Card>
            ) : (
              groupedAppointments.all
                .sort((a: any, b: any) => new Date(b.dateTime).getTime() - new Date(a.dateTime).getTime())
                .map((appointment: any) => (
                  <AppointmentCard key={appointment.appointmentId} appointment={appointment} />
                ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="consultations">
          <div className="space-y-4">
            {groupedAppointments.consultations.map((appointment: any) => (
              <AppointmentCard key={appointment.appointmentId} appointment={appointment} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="vaccinations">
          <div className="space-y-4">
            {groupedAppointments.vaccinations.map((appointment: any) => (
              <AppointmentCard key={appointment.appointmentId} appointment={appointment} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="surgeries">
          <div className="space-y-4">
            {groupedAppointments.surgeries.map((appointment: any) => (
              <AppointmentCard key={appointment.appointmentId} appointment={appointment} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="laboratory">
          <div className="space-y-4">
            {groupedAppointments.laboratory.map((appointment: any) => (
              <AppointmentCard key={appointment.appointmentId} appointment={appointment} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MedicalHistory;
