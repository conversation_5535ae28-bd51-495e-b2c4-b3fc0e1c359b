/**
 * JSON Storage and Querying Examples for MongoDB
 * 
 * This file demonstrates different approaches to store and query JSON data
 * in MongoDB, which can help reduce data complexity and improve performance.
 */

import mongoose from 'mongoose';

// Example 1: Native JSON/Object Storage (Recommended)
const appointmentWithJsonSchema = new mongoose.Schema({
    appointmentId: { type: Number, unique: true },
    
    // Store complex appointment data as native JSON objects
    appointmentData: {
        type: mongoose.Schema.Types.Mixed, // Allows any JSON structure
        index: true
    },
    
    // Store medical data as JSON
    medicalData: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    },
    
    // Store services as JSON array
    servicesData: [{
        type: mongoose.Schema.Types.Mixed
    }],
    
    // Keep essential fields for indexing and fast queries
    petId: { type: Number, index: true },
    clinicId: { type: Number, index: true },
    status: { type: String, index: true },
    dateTime: { type: Date, index: true },
    
    // JSON string storage (alternative approach)
    metadataJson: { type: String }, // Store as JSON string
    
    // Searchable text field for full-text search
    searchableText: { type: String, index: 'text' }
}, { timestamps: true });

// Example queries for native JSON objects:

// 1. Query nested JSON fields
const queryNestedJson = async () => {
    // Find appointments where appointmentData.type is "emergency"
    const emergencyAppointments = await AppointmentModel.find({
        'appointmentData.type': 'emergency'
    });
    
    // Find appointments with specific medical conditions
    const specificConditions = await AppointmentModel.find({
        'medicalData.conditions.diabetes': { $exists: true }
    });
    
    // Query array elements in JSON
    const withSpecificService = await AppointmentModel.find({
        'servicesData.serviceType': 'vaccination'
    });
    
    return { emergencyAppointments, specificConditions, withSpecificService };
};

// 2. Query JSON strings (when stored as strings)
const queryJsonStrings = async () => {
    // Use regex to search within JSON strings
    const jsonStringQuery = await AppointmentModel.find({
        metadataJson: { $regex: /"priority":\s*"high"/, $options: 'i' }
    });
    
    // Use text search for full-text search
    const textSearch = await AppointmentModel.find({
        $text: { $search: "emergency vaccination" }
    });
    
    return { jsonStringQuery, textSearch };
};

// 3. Advanced JSON queries with aggregation
const advancedJsonQueries = async () => {
    // Aggregation pipeline for complex JSON queries
    const complexQuery = await AppointmentModel.aggregate([
        // Match basic criteria
        { $match: { status: 'completed' } },
        
        // Add computed fields from JSON data
        {
            $addFields: {
                hasEmergencyService: {
                    $anyElementTrue: {
                        $map: {
                            input: '$servicesData',
                            as: 'service',
                            in: { $eq: ['$$service.type', 'emergency'] }
                        }
                    }
                },
                totalServiceCost: {
                    $sum: {
                        $map: {
                            input: '$servicesData',
                            as: 'service',
                            in: '$$service.cost'
                        }
                    }
                }
            }
        },
        
        // Filter by computed fields
        { $match: { hasEmergencyService: true } },
        
        // Group and calculate statistics
        {
            $group: {
                _id: '$clinicId',
                totalEmergencyAppointments: { $sum: 1 },
                averageCost: { $avg: '$totalServiceCost' },
                appointments: { $push: '$$ROOT' }
            }
        }
    ]);
    
    return complexQuery;
};

// Example 2: Optimized JSON Storage Model
const optimizedJsonSchema = new mongoose.Schema({
    // Essential indexed fields for fast queries
    id: { type: Number, unique: true },
    entityType: { type: String, index: true }, // 'appointment', 'pet', 'client'
    clinicId: { type: Number, index: true },
    relatedIds: [{ type: Number, index: true }], // petId, clientId, staffId, etc.
    status: { type: String, index: true },
    dateCreated: { type: Date, index: true },
    
    // All complex data stored as JSON
    data: {
        type: mongoose.Schema.Types.Mixed,
        required: true
    },
    
    // Searchable keywords extracted from JSON
    keywords: [{ type: String, index: true }],
    
    // Categories for filtering
    categories: [{ type: String, index: true }],
    
    // Full-text search field
    searchText: { type: String, index: 'text' }
}, { timestamps: true });

// Example usage of optimized model
const optimizedQueries = async () => {
    // Store appointment data
    const appointmentData = {
        appointmentDetails: {
            type: 'consultation',
            duration: 30,
            priority: 'high',
            reason: 'Annual checkup'
        },
        petInfo: {
            name: 'Buddy',
            species: 'Dog',
            breed: 'Golden Retriever',
            age: 5
        },
        services: [
            { name: 'Physical Exam', cost: 50, category: 'examination' },
            { name: 'Vaccination', cost: 30, category: 'prevention' }
        ],
        medicalHistory: {
            allergies: ['peanuts'],
            conditions: ['hip dysplasia'],
            medications: ['glucosamine']
        }
    };
    
    // Create record with extracted keywords and search text
    const record = await OptimizedModel.create({
        entityType: 'appointment',
        clinicId: 1001,
        relatedIds: [2001, 3001], // petId, clientId
        status: 'scheduled',
        data: appointmentData,
        keywords: ['consultation', 'dog', 'vaccination', 'checkup'],
        categories: ['examination', 'prevention'],
        searchText: 'Buddy Golden Retriever consultation vaccination checkup'
    });
    
    // Fast queries using indexed fields
    const fastQueries = {
        // Find all dog appointments
        dogAppointments: await OptimizedModel.find({
            keywords: 'dog',
            entityType: 'appointment'
        }),
        
        // Find appointments with vaccinations
        vaccinationAppointments: await OptimizedModel.find({
            categories: 'prevention',
            'data.services.name': 'Vaccination'
        }),
        
        // Text search
        textSearch: await OptimizedModel.find({
            $text: { $search: 'Golden Retriever vaccination' }
        }),
        
        // Complex JSON queries
        highPriorityConsultations: await OptimizedModel.find({
            'data.appointmentDetails.priority': 'high',
            'data.appointmentDetails.type': 'consultation'
        })
    };
    
    return fastQueries;
};

// Example 3: Hybrid approach - Critical fields + JSON storage
const hybridSchema = new mongoose.Schema({
    // Critical fields that need fast queries (indexed)
    appointmentId: { type: Number, unique: true },
    petId: { type: Number, index: true },
    clinicId: { type: Number, index: true },
    staffId: { type: Number, index: true },
    status: { type: String, index: true },
    appointmentDate: { type: Date, index: true },
    totalCost: { type: Number, index: true },
    
    // All other data as JSON
    extendedData: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    }
});

// Performance comparison example
const performanceComparison = async () => {
    console.log('=== Performance Comparison ===');
    
    // Traditional approach (multiple collections/references)
    console.time('Traditional Query');
    const traditionalResult = await Appointment.find({ status: 'completed' })
        .populate('petId')
        .populate('staffInCharge')
        .populate('clientId');
    console.timeEnd('Traditional Query');
    
    // JSON approach (single collection)
    console.time('JSON Query');
    const jsonResult = await OptimizedModel.find({
        status: 'completed',
        entityType: 'appointment'
    });
    console.timeEnd('JSON Query');
    
    console.log(`Traditional: ${traditionalResult.length} records`);
    console.log(`JSON: ${jsonResult.length} records`);
};

export {
    appointmentWithJsonSchema,
    optimizedJsonSchema,
    hybridSchema,
    queryNestedJson,
    queryJsonStrings,
    advancedJsonQueries,
    optimizedQueries,
    performanceComparison
};
