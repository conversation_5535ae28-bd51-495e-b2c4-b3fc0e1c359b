/**
 * Test the simplified appointment workflow
 */

const API_BASE_URL = 'http://localhost:5000/api';

class SimplifiedWorkflowTester {
    constructor() {
        this.testAppointmentId = null;
        this.testStaffId = null;
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        const url = `${API_BASE_URL}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-token' // You'll need a valid token
            }
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(url, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${result.message || 'Request failed'}`);
            }
            
            return result;
        } catch (error) {
            console.error(`API call failed for ${method} ${endpoint}:`, error.message);
            throw error;
        }
    }

    async testGetAppointment() {
        console.log('\n🔍 Testing: Get appointment data...');
        
        try {
            // First get all appointments to find one to test with
            const appointmentsResponse = await this.apiCall('/appointments?limit=1');
            
            if (!appointmentsResponse.success || !appointmentsResponse.data?.data?.length) {
                console.log('❌ No appointments found to test with');
                return false;
            }

            const appointment = appointmentsResponse.data.data[0];
            this.testAppointmentId = appointment.appointmentId;
            
            console.log(`✅ Found test appointment: ${this.testAppointmentId}`);
            console.log(`   Pet: ${appointment.petName}`);
            console.log(`   Client: ${appointment.clientName}`);
            console.log(`   Status: ${appointment.status}`);
            console.log(`   Appointment Types: ${appointment.appointmentTypes?.length || 0}`);
            
            return true;
        } catch (error) {
            console.error('❌ Failed to get appointment:', error.message);
            return false;
        }
    }

    async testGetStaff() {
        console.log('\n👥 Testing: Get available staff...');
        
        try {
            const staffResponse = await this.apiCall('/staff?status=1&limit=5');
            
            if (!staffResponse.success || !staffResponse.data?.data?.length) {
                console.log('❌ No staff found');
                return false;
            }

            const staff = staffResponse.data.data[0];
            this.testStaffId = staff.staffId;
            
            console.log(`✅ Found test staff: ${staff.firstName} ${staff.lastName} (ID: ${this.testStaffId})`);
            
            return true;
        } catch (error) {
            console.error('❌ Failed to get staff:', error.message);
            return false;
        }
    }

    async testAssignStaff() {
        console.log('\n👨‍⚕️ Testing: Assign staff to appointment type...');
        
        if (!this.testAppointmentId || !this.testStaffId) {
            console.log('❌ Missing test appointment or staff ID');
            return false;
        }

        try {
            // Get the appointment to find a category to assign staff to
            const appointmentResponse = await this.apiCall(`/appointments/${this.testAppointmentId}`);
            
            if (!appointmentResponse.success || !appointmentResponse.data?.appointmentTypes?.length) {
                console.log('❌ No appointment types found to assign staff to');
                return false;
            }

            const appointmentType = appointmentResponse.data.appointmentTypes[0];
            const categoryId = appointmentType.categoryId;

            // Assign staff to this category
            const assignResponse = await this.apiCall(
                `/appointments/${this.testAppointmentId}/assign-staff`,
                'POST',
                {
                    categoryId: categoryId,
                    staffId: this.testStaffId
                }
            );

            if (assignResponse.success) {
                console.log(`✅ Successfully assigned staff to category: ${appointmentType.categoryName}`);
                return true;
            } else {
                console.log('❌ Failed to assign staff:', assignResponse.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to assign staff:', error.message);
            return false;
        }
    }

    async testUpdateAppointment() {
        console.log('\n📝 Testing: Update appointment notes and status...');
        
        if (!this.testAppointmentId) {
            console.log('❌ Missing test appointment ID');
            return false;
        }

        try {
            const updateResponse = await this.apiCall(
                `/appointments/${this.testAppointmentId}`,
                'PUT',
                {
                    generalNotes: 'Test notes from simplified workflow',
                    recommendations: 'Test recommendations from simplified workflow',
                    status: 'in_progress'
                }
            );

            if (updateResponse.success) {
                console.log('✅ Successfully updated appointment');
                console.log('   Notes and recommendations added');
                console.log('   Status updated to in_progress');
                return true;
            } else {
                console.log('❌ Failed to update appointment:', updateResponse.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to update appointment:', error.message);
            return false;
        }
    }

    async testCompleteAppointment() {
        console.log('\n✅ Testing: Complete appointment...');
        
        if (!this.testAppointmentId) {
            console.log('❌ Missing test appointment ID');
            return false;
        }

        try {
            const completeResponse = await this.apiCall(
                `/appointments/${this.testAppointmentId}`,
                'PUT',
                {
                    status: 'completed',
                    completionStatus: 'completed'
                }
            );

            if (completeResponse.success) {
                console.log('✅ Successfully completed appointment');
                return true;
            } else {
                console.log('❌ Failed to complete appointment:', completeResponse.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to complete appointment:', error.message);
            return false;
        }
    }

    async runTest() {
        console.log('🚀 Starting Simplified Appointment Workflow Test');
        console.log('=================================================\n');

        try {
            // Test sequence
            const tests = [
                { name: 'Get Appointment', fn: () => this.testGetAppointment() },
                { name: 'Get Staff', fn: () => this.testGetStaff() },
                { name: 'Assign Staff', fn: () => this.testAssignStaff() },
                { name: 'Update Appointment', fn: () => this.testUpdateAppointment() },
                { name: 'Complete Appointment', fn: () => this.testCompleteAppointment() }
            ];

            let passed = 0;
            let failed = 0;

            for (const test of tests) {
                const result = await test.fn();
                if (result) {
                    passed++;
                } else {
                    failed++;
                }
            }

            console.log('\n📊 TEST RESULTS');
            console.log('================');
            console.log(`✅ Passed: ${passed}`);
            console.log(`❌ Failed: ${failed}`);
            console.log(`📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);

            if (failed === 0) {
                console.log('\n🎉 ALL TESTS PASSED! Simplified workflow is working correctly.');
            } else {
                console.log('\n⚠️  Some tests failed. Please check the implementation.');
            }

        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
        }
    }
}

// Run the test
const tester = new SimplifiedWorkflowTester();
tester.runTest().catch(console.error);
