const http = require('http');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODMxM2Q3Nzcw' +
              'YzIwMjFlOGFmZjBmNzMiLCJpYXQiOjE3NDgxNzcwMjksImV4cCI6MTc0ODI2MzQyOX0.' +
              'rwLk7PHmaLRf2AKMd5wBUkZRbpbY8kvMNWQCrcXm1j0';

// Test fetching the service we just created by serviceId
console.log('Testing service fetch by serviceId to check if appointmentId was saved...');

const fetchOptions = {
  hostname: 'localhost',
  port: 5500,
  path: '/api/v1/services/1020', // The serviceId from our previous test
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`
  }
};

const fetchReq = http.request(fetchOptions, (res) => {
  console.log('Fetch Status:', res.statusCode);
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    console.log('Fetch Response:', body);
    
    // Parse and check if appointmentId exists
    try {
      const response = JSON.parse(body);
      if (response.success && response.data) {
        console.log('\nChecking appointmentId field:');
        console.log('appointmentId:', response.data.appointmentId);
        console.log('Has appointmentId field:', 'appointmentId' in response.data);
      }
    } catch (e) {
      console.error('Error parsing response:', e.message);
    }
  });
});

fetchReq.on('error', (e) => {
  console.error('Fetch Error:', e.message);
});

fetchReq.end();
