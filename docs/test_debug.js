const http = require('http');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODMxM2Q3Nzcw' +
              'YzIwMjFlOGFmZjBmNzMiLCJpYXQiOjE3NDgxNzcyMzUsImV4cCI6MTc0ODI2MzYzNX0.' +
              'enPOgBcm0x5m9Y0DmJDyEOEfJyoz6eOBCtEkF2ugRhI';

// Test data with appointmentId
const serviceData = {
  serviceName: 'Debug Test Service 2',
  description: 'Testing appointmentId debug with fresh token',
  defaultPrice: 250,
  estimatedDuration: 45,
  category: 'consultation',
  currency: 'KES',
  appointmentId: 8888,
  isActive: true,
  isCustom: true
};

const data = JSON.stringify(serviceData);

console.log('Testing service creation with debug logging...');
console.log('Sending data:', JSON.stringify(serviceData, null, 2));

const options = {
  hostname: 'localhost',
  port: 5500,
  path: '/api/v1/services',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'Content-Length': data.length
  }
};

const req = http.request(options, (res) => {
  console.log('Status:', res.statusCode);

  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });

  res.on('end', () => {
    console.log('Response:', body);
  });
});

req.on('error', (e) => {
  console.error('Error:', e.message);
});

req.write(data);
req.end();
