# 🚀 Veterinary SaaS System - Production Deployment Guide

## 📋 **System Overview**

This is a comprehensive multi-clinic veterinary SaaS platform with AI integration, real-time features, and modern architecture.

### **Key Features**
- ✅ Multi-clinic SaaS architecture
- ✅ AI-powered appointment suggestions and medical recommendations
- ✅ Real-time WebSocket notifications
- ✅ Unified appointment booking (registered + walk-in clients)
- ✅ Role-based permissions and staff management
- ✅ Comprehensive billing and invoice system
- ✅ Cross-clinic patient referrals
- ✅ Emergency detection and prioritization

## 🛠 **Prerequisites**

### **System Requirements**
- Node.js 18+ 
- MongoDB 6.0+
- Redis 6.0+ (for sessions and caching)
- SSL Certificate (for HTTPS)
- Domain name

### **External Services**
- OpenAI API key (for AI features)
- Email service (SMTP or SendGrid)
- SMS service (Twilio - optional)
- Cloud storage (AWS S3 or similar)

## 📦 **Installation & Setup**

### **1. Clone and Install Dependencies**
```bash
git clone <repository-url>
cd vet-care-systems
npm install
```

### **2. Environment Configuration**
Create production environment file:
```bash
cp .env.example .env.production.local
```

Configure the following variables:
```env
# Database
DB_URI=mongodb://your-mongodb-connection-string
REDIS_URL=redis://your-redis-connection-string

# Server
NODE_ENV=production
PORT=5500
DOMAIN=your-domain.com

# Security
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=7d
BCRYPT_SALT_ROUNDS=12

# AI Integration
OPENAI_API_KEY=your-openai-api-key
AI_ENABLED=true

# Email Service
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# SMS Service (Optional)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=your-twilio-phone

# File Storage
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_BUCKET_NAME=your-s3-bucket
AWS_REGION=us-east-1

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info
```

### **3. Database Setup**
```bash
# Start MongoDB service
sudo systemctl start mongod

# Initialize database with default data
npm run db:seed
```

### **4. Build and Start**
```bash
# Install PM2 for process management
npm install -g pm2

# Start the application
pm2 start ecosystem.config.js --env production
```

## 🔧 **Production Configuration**

### **PM2 Ecosystem Configuration**
Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'vet-care-api',
    script: 'app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5500
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### **Nginx Configuration**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:5500;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:5500;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔐 **Security Configuration**

### **Firewall Setup**
```bash
# Allow SSH, HTTP, and HTTPS
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### **SSL Certificate (Let's Encrypt)**
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### **Database Security**
```bash
# Enable MongoDB authentication
sudo nano /etc/mongod.conf

# Add:
security:
  authorization: enabled

# Create admin user
mongo
use admin
db.createUser({
  user: "admin",
  pwd: "secure-password",
  roles: ["userAdminAnyDatabase"]
})
```

## 📊 **Monitoring & Logging**

### **Health Checks**
The system includes built-in health check endpoints:
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed system status

### **Log Management**
```bash
# View application logs
pm2 logs vet-care-api

# Monitor system resources
pm2 monit
```

### **Database Monitoring**
```bash
# MongoDB status
sudo systemctl status mongod

# Check database performance
mongo --eval "db.stats()"
```

## 🚀 **Deployment Steps**

### **1. Server Preparation**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Install Nginx
sudo apt install nginx -y
```

### **2. Application Deployment**
```bash
# Clone repository
git clone <your-repo-url> /var/www/vet-care-systems
cd /var/www/vet-care-systems

# Install dependencies
npm ci --production

# Set up environment
cp .env.example .env.production.local
# Edit .env.production.local with your values

# Start services
sudo systemctl start mongod
sudo systemctl enable mongod

# Start application
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### **3. Frontend Deployment**
```bash
cd lovable-vetcare
npm ci
npm run build

# Copy build files to web server
sudo cp -r dist/* /var/www/html/
```

## 🔄 **Maintenance & Updates**

### **Regular Maintenance**
```bash
# Update application
git pull origin main
npm ci --production
pm2 reload vet-care-api

# Database backup
mongodump --out /backup/$(date +%Y%m%d)

# Log rotation
pm2 flush
```

### **Performance Optimization**
- Enable MongoDB indexing
- Configure Redis caching
- Optimize Nginx gzip compression
- Set up CDN for static assets

## 🆘 **Troubleshooting**

### **Common Issues**

**MongoDB Connection Issues:**
```bash
# Check MongoDB status
sudo systemctl status mongod

# Check logs
sudo tail -f /var/log/mongodb/mongod.log
```

**Application Not Starting:**
```bash
# Check PM2 status
pm2 status

# View error logs
pm2 logs vet-care-api --err
```

**High Memory Usage:**
```bash
# Monitor resources
pm2 monit

# Restart application
pm2 restart vet-care-api
```

## 📞 **Support & Contact**

For technical support and deployment assistance:
- Documentation: `/docs/`
- API Documentation: `/api/docs`
- Health Check: `/health`

## 🎯 **Post-Deployment Checklist**

- [ ] SSL certificate installed and working
- [ ] Database backup scheduled
- [ ] Monitoring alerts configured
- [ ] Performance testing completed
- [ ] Security audit passed
- [ ] User acceptance testing completed
- [ ] Documentation updated
- [ ] Team training completed

---

**🎉 Congratulations! Your Veterinary SaaS System is now live and ready to revolutionize veterinary practice management!**
