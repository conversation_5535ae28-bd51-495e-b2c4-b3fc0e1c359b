# Mongoose Duplicate Index Fix

## Issue

The backend is showing warnings about duplicate schema indexes:

```
[MONGOOSE] Warning: Duplicate schema index on {"roleId":1} found.
[MONGOOSE] Warning: Duplicate schema index on {"name":1} found.
```

These warnings occur because some schemas are defining indexes in two ways:

1. Using the `index: true` or `unique: true` in the schema field definition
2. Using explicit `schema.index()` calls after the schema definition

## Affected Models

The following models have duplicate index definitions:

1. **Role Model** (`models/role.model.js`)
   - Duplicate index on `roleId`
   - Duplicate index on `name`

2. **Permission Model** (`models/permission.model.js`)
   - Duplicate index on `permissionId`
   - Duplicate index on `name`

3. **Species Model** (`models/species.model.js`)
   - Duplicate index on `name`

4. **ServiceType Model** (`models/serviceType.model.js`)
   - Duplicate index on `name`

## Fix Approach

For each affected model, we need to choose one approach for defining indexes:

### Option 1: Keep field-level indexes only

Remove the explicit `schema.index()` calls and rely on the field-level `unique: true` or `index: true` properties.

### Option 2: Keep explicit index definitions only

Remove the `unique: true` or `index: true` from the schema field definitions and keep the explicit `schema.index()` calls.

## Recommended Fix

Option 2 (keeping explicit index definitions) is generally preferred because:

1. It centralizes all index definitions in one place
2. It allows for more complex index options
3. It makes indexes more visible and easier to manage

## Implementation Steps

For each affected model:

1. Remove `unique: true` from the schema field definition
2. Keep the explicit `schema.index()` call with the `unique: true` option

### Example Fix for Role Model

```javascript
// Before
const roleSchema = new mongoose.Schema({
    roleId: {
        type: Number,
        unique: true  // Remove this
    },
    name: {
        type: String,
        required: [true, 'Role name is required'],
        unique: true,  // Remove this
        trim: true
    },
    // ...
});

roleSchema.plugin(AutoIncrement, { inc_field: 'roleId', start_seq: 1001 });
roleSchema.index({ roleId: 1 }, { unique: true });  // Keep this
roleSchema.index({ name: 1 }, { unique: true });    // Keep this

// After
const roleSchema = new mongoose.Schema({
    roleId: {
        type: Number
    },
    name: {
        type: String,
        required: [true, 'Role name is required'],
        trim: true
    },
    // ...
});

roleSchema.plugin(AutoIncrement, { inc_field: 'roleId', start_seq: 1001 });
roleSchema.index({ roleId: 1 }, { unique: true });
roleSchema.index({ name: 1 }, { unique: true });
```

### Fix for Permission Model

```javascript
// Before
const permissionSchema = new mongoose.Schema({
    permissionId: {
        type: Number,
        unique: true  // Remove this
    },
    name: {
        type: String,
        required: [true, 'Permission name is required'],
        unique: true,  // Remove this
        trim: true
    },
    // ...
});

// After
const permissionSchema = new mongoose.Schema({
    permissionId: {
        type: Number
    },
    name: {
        type: String,
        required: [true, 'Permission name is required'],
        trim: true
    },
    // ...
});
```

### Fix for Species Model

```javascript
// Before
const speciesSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, "Species name is required"],
        unique: true,  // Remove this
        trim: true
    },
    // ...
});

// After
const speciesSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, "Species name is required"],
        trim: true
    },
    // ...
});
```

### Fix for ServiceType Model

```javascript
// Before
const serviceTypeSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, "Service type name is required"],
        unique: true,  // Remove this
        trim: true,
    },
    // ...
});

// After
const serviceTypeSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, "Service type name is required"],
        trim: true,
    },
    // ...
});
```

## Benefits

Fixing these warnings will:

1. Eliminate confusing console warnings
2. Prevent potential performance issues
3. Make the code more maintainable
4. Follow MongoDB best practices

## Note

These warnings don't prevent the application from running, but they should be addressed to maintain a clean codebase and prevent potential issues in the future.
