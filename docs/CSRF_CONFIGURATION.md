# CSRF Protection Configuration

## Issue

After implementing CSRF protection, the server is showing errors for requests without valid CSRF tokens:

```
Error: {
  message: 'invalid csrf token',
  code: 'EBADCSRFTOKEN'
}
```

This is expected behavior for CSRF protection, but we need to ensure that:

1. The frontend can obtain a valid CSRF token
2. The token is included in all non-GET requests
3. API testing tools can work with the CSRF protection

## Solution

### 1. Modify CSRF Configuration for Development

For development purposes, we can make CSRF protection more flexible:

```javascript
// In middlewares/csrf.middleware.js
const csrfProtection = csrf({
  cookie: {
    key: '_csrf',
    path: '/',
    httpOnly: true,
    secure: NODE_ENV === 'production',
    sameSite: NODE_ENV === 'production' ? 'strict' : 'lax' // Use 'lax' in development
  },
  ignoreMethods: NODE_ENV === 'development' 
    ? ['GET', 'HEAD', 'OPTIONS', 'POST', 'PUT', 'DELETE'] // Ignore all methods in development
    : ['GET', 'HEAD', 'OPTIONS'] // Only ignore safe methods in production
});
```

### 2. Create a CSRF Token Endpoint

Add an endpoint to provide CSRF tokens to clients:

```javascript
// In routes/v1/csrf.routes.js
import { Router } from "express";
import { csrfProtection } from "../../middlewares/csrf.middleware.js";

const csrfRouter = Router();

/**
 * @route GET /api/v1/csrf-token
 * @desc Get a CSRF token
 * @access Public
 */
csrfRouter.get('/csrf-token', csrfProtection, (req, res) => {
  return res.json({
    success: true,
    status: 200,
    message: 'CSRF token generated',
    data: {
      csrfToken: req.csrfToken()
    }
  });
});

export default csrfRouter;
```

### 3. Update the Frontend API Service

Update the frontend API service to fetch and use CSRF tokens:

```typescript
// In src/services/api.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { useAuthStore } from '../store';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5500/api/v1',
  withCredentials: true, // Important for CSRF cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include CSRF token
api.interceptors.request.use(async (config) => {
  // Only add CSRF token for non-GET requests
  if (config.method !== 'get') {
    try {
      // Get CSRF token if we don't have one
      if (!window.csrfToken) {
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL || 'http://localhost:5500/api/v1'}/csrf-token`,
          { withCredentials: true }
        );
        window.csrfToken = response.data.data.csrfToken;
      }
      
      // Add CSRF token to headers
      config.headers['X-CSRF-Token'] = window.csrfToken;
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error);
    }
  }
  
  // Add auth token if available
  const token = useAuthStore.getState().token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  return config;
});

export { api };
```

### 4. Add CSRF Token to Postman/API Testing Tools

For testing with tools like Postman:

1. First make a GET request to `/api/v1/csrf-token` with cookies enabled
2. Extract the CSRF token from the response
3. Add the token as an `X-CSRF-Token` header in subsequent requests

## Development Mode Configuration

For easier development, you can temporarily disable CSRF protection:

```javascript
// In app.js
// CSRF protection (disable in development for easier testing)
if (NODE_ENV === 'production') {
  app.use(handleCSRFError);
  
  // CSRF protection for non-GET routes
  const csrfProtectedRoutes = express.Router();
  csrfProtectedRoutes.use(csrfProtection);
  csrfProtectedRoutes.use(provideCSRFToken);
  
  // Mount versioned API routes with CSRF protection
  app.use('/api/v1', (req, res, next) => {
    if (req.method === 'GET') {
      return next();
    }
    csrfProtectedRoutes(req, res, next);
  }, v1Routes);
} else {
  // In development, don't use CSRF protection
  app.use('/api/v1', v1Routes);
}
```

## Security Considerations

- CSRF protection should always be enabled in production
- The token should be transmitted in headers, not in the URL
- Cookies should be set with appropriate security flags
- Consider using SameSite=Strict for cookies in production

## Testing CSRF Protection

To test that CSRF protection is working correctly:

1. Make a GET request to `/api/v1/csrf-token` to get a token
2. Make a POST request with the token in the header - should succeed
3. Make a POST request without the token - should fail with 403 Forbidden
4. Make a POST request with an invalid token - should fail with 403 Forbidden
