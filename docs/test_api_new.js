const http = require('http');

// First, let's get a fresh token
const loginData = JSON.stringify({
  email: '<EMAIL>',
  password: 'pass123'
});

const loginOptions = {
  hostname: 'localhost',
  port: 5500,
  path: '/api/auth/sign-in',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': loginData.length
  }
};

console.log('Getting fresh token...');
const loginReq = http.request(loginOptions, (res) => {
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });

  res.on('end', () => {
    try {
      const loginResponse = JSON.parse(body);
      if (loginResponse.success && loginResponse.data.token) {
        console.log('Token obtained successfully');
        testServiceCreation(loginResponse.data.token);
      } else {
        console.error('Login failed:', body);
      }
    } catch (e) {
      console.error('Error parsing login response:', e.message);
    }
  });
});

loginReq.on('error', (e) => {
  console.error('Login error:', e.message);
});

loginReq.write(loginData);
loginReq.end();

function testServiceCreation(token) {
  // Test data without appointmentId (services are now linked through appointment.serviceCategories)
  const serviceData = {
    serviceName: 'New Test Service',
    description: 'Testing service creation without appointmentId',
    defaultPrice: 150,
    estimatedDuration: 45,
    category: 'consultation',
    currency: 'KES',
    isActive: true,
    isCustom: true
  };

  const data = JSON.stringify(serviceData);

  console.log('\nTesting service creation without appointmentId');
  const options = {
    hostname: 'localhost',
    port: 5500,
    path: '/api/v1/services',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Content-Length': data.length
    }
  };

  const req = http.request(options, (res) => {
    console.log('Status:', res.statusCode);

    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });

    res.on('end', () => {
      console.log('Response:', body);

      try {
        const response = JSON.parse(body);
        if (response.success && response.data) {
          const serviceId = response.data.serviceId;
          console.log('\nService created with ID:', serviceId);
          console.log('appointmentId in response:', response.data.appointmentId);

          // Test fetching the service by ID
          setTimeout(() => testServiceFetch(token, serviceId), 1000);
        }
      } catch (e) {
        console.error('Error parsing response:', e.message);
      }
    });
  });

  req.on('error', (e) => {
    console.error('Error:', e.message);
  });

  req.write(data);
  req.end();
}

function testServiceFetch(token, serviceId) {
  console.log('\nTesting service fetch by ID:', serviceId);

  const fetchOptions = {
    hostname: 'localhost',
    port: 5500,
    path: `/api/v1/services/${serviceId}`,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const fetchReq = http.request(fetchOptions, (res) => {
    console.log('Fetch Status:', res.statusCode);

    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });

    res.on('end', () => {
      console.log('Fetch Response:', body);

      try {
        const response = JSON.parse(body);
        if (response.success && response.data) {
          console.log('\nChecking appointmentId in fetched service:');
          console.log('appointmentId:', response.data.appointmentId);
          console.log('Has appointmentId field:', 'appointmentId' in response.data);

          // Test filtering by appointmentId
          setTimeout(() => testServiceFilter(token, 5678), 1000);
        }
      } catch (e) {
        console.error('Error parsing fetch response:', e.message);
      }
    });
  });

  fetchReq.on('error', (e) => {
    console.error('Fetch Error:', e.message);
  });

  fetchReq.end();
}

function testServiceFilter(token, appointmentId) {
  console.log('\nTesting service filter by appointmentId:', appointmentId);

  const filterOptions = {
    hostname: 'localhost',
    port: 5500,
    path: `/api/v1/services?appointmentId=${appointmentId}&limit=10`,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const filterReq = http.request(filterOptions, (res) => {
    console.log('Filter Status:', res.statusCode);

    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });

    res.on('end', () => {
      console.log('Filter Response:', body);

      try {
        const response = JSON.parse(body);
        if (response.success && response.data) {
          console.log('\nFiltered services count:', response.data.services.length);
          if (response.data.services.length > 0) {
            console.log('First service appointmentId:', response.data.services[0].appointmentId);
          }
        }
      } catch (e) {
        console.error('Error parsing filter response:', e.message);
      }
    });
  });

  filterReq.on('error', (e) => {
    console.error('Filter Error:', e.message);
  });

  filterReq.end();
}
