# Authentication System Improvements Guide

This guide outlines the steps to improve the authentication system in the VetCare SaaS application, addressing both frontend and backend issues.

## Current Issues

1. **Backend Signup Issue**:
   - Error: `Clinic validation failed: ownerId: Owner ID is required`
   - Root cause: When creating a clinic during signup, the ownerId is initially set to null, but the clinic schema requires it

2. **Frontend Authentication Management**:
   - Multiple auth stores causing confusion (`src/store/auth.ts` and `src/store/slices/authSlice.ts`)
   - Token access is not centralized, making it difficult for API calls to consistently get the token

3. **Sign-in Logic**:
   - Need to improve sign-in to check both user and staff tables if user is not found in the first table

## Step-by-Step Improvement Plan

### 1. Fix Backend Signup Issue

1. **Update Clinic Schema**:
   - Modify the clinic model to make ownerId optional during creation
   - Update validation to check ownerId only after initial creation

2. **Improve Signup Transaction**:
   - Ensure proper order of operations in the transaction
   - Create user first, then create clinic with the user ID

### 2. Centralize Frontend Authentication

1. **Consolidate Auth Stores**:
   - Keep only one auth store (`src/store/slices/authSlice.ts`)
   - Update all components to use this single store

2. **Enhance API Service**:
   - Ensure token is consistently accessed from the central store
   - Improve error handling for authentication errors

3. **Update Auth Types**:
   - Create comprehensive types for authentication data
   - Ensure proper typing for user, staff, and clinic data

### 3. Improve Sign-in Logic

1. **Enhance Sign-in Controller**:
   - Implement fallback logic to check multiple user tables
   - Maintain proper user type tracking

## Implementation Steps

### Backend Changes

#### 1. Update Clinic Model

```javascript
// vet-care-systems/models/clinic.model.js
const clinicSchema = new mongoose.Schema(
    {
        // ...other fields
        ownerId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
            // Make it required but with a custom validator
            required: [function() {
                // Skip validation during initial creation
                return !this.isNew;
            }, "Owner ID is required"],
        },
        // ...other fields
    },
    { timestamps: true }
);
```

#### 2. Fix Signup Controller

```javascript
// vet-care-systems/controllers/auth.controller.js
export const signUp = async (req, res, next) => {
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        // ...existing code

        // Create new user first
        const newUser = await UserModel.create(
            [userData],
            { session }
        );

        // Then create clinic with the user ID if needed
        if (is_clinic_owner && clinic_data) {
            const [newClinic] = await Clinic.create([{
                clinicName: clinic_data.name,
                ownerId: newUser[0]._id, // Set owner ID immediately
                phoneNumber: clinic_data.phoneNumber,
                email: clinic_data.email,
                address: clinic_data.address,
                status: 1
            }], { session });

            clinicId = newClinic._id;
            
            // Update user with clinic info if needed
            // ...
        }

        // ...rest of the function
    } catch (error) {
        // ...error handling
    }
};
```

#### 3. Enhance Sign-in Controller

```javascript
// vet-care-systems/controllers/auth.controller.js
export const signIn = async (req, res) => {
    try {
        let { email, password, userType = 'client' } = req.body;
        
        // Special case for admin
        if (email === '<EMAIL>') {
            userType = 'user';
        }

        // Try to find user in the specified model
        let UserModel = userType === 'staff' ? Staff : 
                        userType === 'user' ? User : Client;
        
        let user = await UserModel.findOne({ email })
            .select('firstName middleName lastName email phoneNumber password status lastLogin loginCount role')
            .lean();
        
        // If not found and not trying admin, try staff as fallback
        if (!user && userType !== 'staff' && userType !== 'user') {
            UserModel = Staff;
            user = await UserModel.findOne({ email })
                .select('firstName middleName lastName email phoneNumber password status lastLogin loginCount role')
                .lean();
            
            if (user) {
                userType = 'staff';
            }
        }
        
        // If still not found and not trying user, try system user as fallback
        if (!user && userType !== 'user') {
            UserModel = User;
            user = await UserModel.findOne({ email })
                .select('firstName middleName lastName email phoneNumber password status lastLogin loginCount role')
                .lean();
            
            if (user) {
                userType = 'user';
            }
        }

        // Continue with existing login logic...
    } catch (error) {
        // ...error handling
    }
};
```

### Frontend Changes

#### 1. Centralize Auth Store

```typescript
// lovable-vetcare/src/store/index.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, Staff, Clinic } from './types';
import { api } from '@/services/api';

// Define a single auth store
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  staff: Staff | null;
  availableClinics: Clinic[];
  currentClinic: Clinic | null;
  // ...other state
  
  // Actions
  login: (email: string, password: string) => Promise<any>;
  logout: () => void;
  getToken: () => string | null;
  updateUser: (user: User) => void;
  updateStaff: (staff: Staff) => void;
  // ...other actions
}

// Create the store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      user: null,
      token: null,
      staff: null,
      availableClinics: [],
      currentClinic: null,
      
      // Actions
      login: async (email, password) => {
        // Implementation
      },
      logout: () => {
        // Implementation
      },
      getToken: () => get().token,
      updateUser: (user) => set({ user }),
      updateStaff: (staff) => set({ staff }),
      // ...other actions
    }),
    { name: 'auth-storage' }
  )
);

// Export a hook for components to use
export const useAuth = () => {
  const {
    isAuthenticated, user, token, staff, currentClinic, availableClinics,
    login, logout, getToken, updateUser, updateStaff
  } = useAuthStore();
  
  return {
    isAuthenticated, user, token, staff, currentClinic, availableClinics,
    login, logout, getToken, updateUser, updateStaff
  };
};
```

#### 2. Update API Service

```typescript
// lovable-vetcare/src/services/api.ts
import axios from 'axios';
import { useAuthStore } from '@/store';

// Create axios instance
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Skip token for public endpoints
    const isPublicEndpoint = ['/auth/sign-in', '/auth/sign-up'].some(
      endpoint => config.url?.includes(endpoint)
    );
    
    if (!isPublicEndpoint) {
      const token = useAuthStore.getState().token;
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 errors
    if (error.response?.status === 401) {
      useAuthStore.getState().logout();
    }
    return Promise.reject(error);
  }
);

// Export API methods
export const api = {
  get: (url, config) => apiClient.get(url, config),
  post: (url, data, config) => apiClient.post(url, data, config),
  put: (url, data, config) => apiClient.put(url, data, config),
  patch: (url, data, config) => apiClient.patch(url, data, config),
  delete: (url, config) => apiClient.delete(url, config),
};
```

## Testing the Changes

1. **Test Signup**:
   - Register a new clinic owner
   - Verify clinic is created with proper owner ID

2. **Test Sign-in**:
   - Test signing in with different user types
   - Verify fallback logic works when user exists in a different table

3. **Test API Authentication**:
   - Make authenticated API calls
   - Verify token is properly included
   - Test token expiration handling

## Next Steps

After implementing these changes, consider:

1. Adding refresh token functionality
2. Implementing role-based access control
3. Adding multi-factor authentication
4. Improving security with rate limiting and brute force protection
