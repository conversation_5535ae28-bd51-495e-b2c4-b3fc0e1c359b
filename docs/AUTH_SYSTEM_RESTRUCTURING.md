# Authentication System Restructuring Guide

This guide outlines the steps to restructure the authentication system in the VetCare SaaS application to make the User table the main table, with Staff borrowing userId from it. The system will only allow clinic owners and staff to sign up and log in.

## Overview of Changes

1. **Database Structure**:
   - User table becomes the main authentication table
   - Staff table references User table via userId
   - Remove duplicate authentication fields from Staff table

2. **Authentication Flow**:
   - All users are created in the User table first
   - Staff records are created with a reference to the User
   - JWT tokens include both userId and staffId when applicable

3. **Middleware Updates**:
   - Update auth middleware to handle the new token structure
   - Modify permission checks to work with the new relationship

## Step-by-Step Implementation

### 1. Update Staff Model

```javascript
// vet-care-systems/models/staff.model.js
const staffSchema = new mongoose.Schema({
    // Reference to User model (main table)
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, "User ID is required"],
        unique: true
    },
    
    // Remove duplicate fields (email, password, firstName, etc.)
    // Keep only staff-specific fields
    
    // Primary clinic where the staff member works
    clinicId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Clinic',
        required: true
    },
    // ... other fields
});
```

### 2. Update Clinic Model

```javascript
// vet-care-systems/models/clinic.model.js
const clinicSchema = new mongoose.Schema({
    // ... other fields
    ownerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        // Make it conditionally required
        required: [function() {
            // Skip validation during initial creation
            return !this.isNew;
        }, "Owner ID is required"],
    },
    // ... other fields
});
```

### 3. Update Signup Controller

```javascript
// vet-care-systems/controllers/auth.controller.js
export const signUp = async (req, res, next) => {
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        // Extract user data from request
        const {
            firstName, middleName, lastName, email, phoneNumber,
            password, address, dob, is_clinic_owner, clinic_data,
            // Staff-specific fields
            roleId, jobTitle, employmentDate, salary, emergencyContact, schedule
        } = req.body;

        // Check if user exists in User table
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return sendResponse(res, 409, false, "User already exists");
        }

        // Create user in User table
        const [newUser] = await User.create(
            [{
                firstName, middleName, lastName, email, phoneNumber,
                password: hashedPassword, address, dob, status: 1,
                role: is_clinic_owner ? 'clinic_owner' : 'staff'
            }],
            { session }
        );

        // Create clinic if user is a clinic owner
        let clinicId = null;
        if (is_clinic_owner && clinic_data) {
            const [newClinic] = await Clinic.create([{
                clinicName: clinic_data.name,
                ownerId: newUser._id,
                phoneNumber: clinic_data.phoneNumber,
                email: clinic_data.email,
                address: clinic_data.address,
                status: 1
            }], { session });
            
            clinicId = newClinic._id;
        }

        // Create staff record linked to the user
        const [newStaff] = await Staff.create(
            [{
                userId: newUser._id,
                clinicId: clinicId,
                primaryClinicId: clinicId,
                isClinicOwner: is_clinic_owner,
                roleId: roleId || (is_clinic_owner ? 1 : 2),
                jobTitle: jobTitle || (is_clinic_owner ? 'Clinic Owner' : 'Staff'),
                employmentDate: employmentDate || new Date(),
                salary: salary || 0,
                emergencyContact: emergencyContact,
                schedule: schedule,
                status: 1
            }],
            { session }
        );

        // Generate token with both IDs
        const token = jwt.sign(
            {
                userId: newUser._id,
                staffId: newStaff._id
            },
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        // Prepare response
        const responseData = {
            token,
            user: { ...newUser._doc, password: undefined },
            staff: newStaff
        };

        await session.commitTransaction();
        return sendResponse(res, 201, true, "User created successfully", responseData);
    } catch (error) {
        await session.abortTransaction();
        return sendResponse(res, 500, false, error.message);
    } finally {
        session.endSession();
    }
};
```

### 4. Update Signin Controller

```javascript
// vet-care-systems/controllers/auth.controller.js
export const signIn = async (req, res) => {
    try {
        const { email, password } = req.body;
        
        // Find user in User table
        const user = await User.findOne({ email }).select('+password');
        if (!user) {
            return sendResponse(res, 404, false, "User not found");
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return sendResponse(res, 401, false, "Invalid credentials");
        }

        // Update login tracking
        await User.updateOne(
            { _id: user._id },
            {
                $set: { lastLogin: new Date() },
                $inc: { loginCount: 1 }
            }
        );

        // Prepare response
        const responseData = {
            user: { ...user._doc, password: undefined }
        };

        // Handle admin login
        if (user.role === 'admin' || user.role === 'super_admin') {
            const token = jwt.sign(
                { userId: user._id, isAdmin: true },
                JWT_SECRET,
                { expiresIn: JWT_EXPIRES_IN }
            );
            responseData.token = token;
            
            // Get all clinics for admin
            responseData.clinics = await Clinic.find({ status: 1 }).lean();
        } 
        // Handle staff login
        else {
            // Find staff record
            const staff = await Staff.findOne({ userId: user._id });
            if (!staff) {
                return sendResponse(res, 404, false, "Staff record not found");
            }
            
            const token = jwt.sign(
                { userId: user._id, staffId: staff._id },
                JWT_SECRET,
                { expiresIn: JWT_EXPIRES_IN }
            );
            responseData.token = token;
            responseData.staff = staff;
            
            // Get clinics for staff
            // ... (code to get primary and additional clinics)
        }

        return sendResponse(res, 200, true, "Login successful", responseData);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};
```

### 5. Update Auth Middleware

```javascript
// vet-care-systems/middleware/auth.middleware.js
export const verifyToken = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];
        if (!token) {
            return sendResponse(res, 401, false, 'No token provided');
        }

        const decoded = jwt.verify(token, JWT_SECRET);
        
        // Find user in User table
        const user = await User.findById(decoded.userId).select('-password');
        if (!user) {
            return sendResponse(res, 401, false, 'Invalid token');
        }

        req.user = user;
        
        // Check if admin
        req.user.isAdmin = user.role === 'admin' || user.role === 'super_admin';
        
        // If staffId exists, load staff data
        if (decoded.staffId) {
            const staff = await Staff.findById(decoded.staffId);
            if (staff) {
                req.staff = staff;
                req.user.isStaff = true;
                req.user.isClinicOwner = staff.isClinicOwner;
            }
        }

        next();
    } catch (error) {
        // Handle token errors
        return sendResponse(res, 401, false, 'Authentication failed');
    }
};
```

## Frontend Changes

### 1. Update API Service

```typescript
// lovable-vetcare/src/services/api.ts
import axios from 'axios';
import { useAuthStore } from '@/store';

// Create axios instance
const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    headers: { 'Content-Type': 'application/json' }
});

// Add auth token to requests
apiClient.interceptors.request.use(config => {
    const token = useAuthStore.getState().token;
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Handle auth errors
apiClient.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 401) {
            useAuthStore.getState().logout();
        }
        return Promise.reject(error);
    }
);

export const api = {
    get: (url, config) => apiClient.get(url, config),
    post: (url, data, config) => apiClient.post(url, data, config),
    put: (url, data, config) => apiClient.put(url, data, config),
    delete: (url, config) => apiClient.delete(url, config)
};
```

### 2. Centralize Auth Store

```typescript
// lovable-vetcare/src/store/auth.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const useAuthStore = create(
    persist(
        (set, get) => ({
            isAuthenticated: false,
            user: null,
            staff: null,
            token: null,
            
            login: async (email, password) => {
                // Implementation
            },
            
            logout: () => {
                set({
                    isAuthenticated: false,
                    user: null,
                    staff: null,
                    token: null
                });
                localStorage.removeItem('auth-storage');
                window.location.href = '/login';
            },
            
            getToken: () => get().token
        }),
        { name: 'auth-storage' }
    )
);
```

## Testing the Changes

1. **Test User Creation**:
   - Create a new clinic owner
   - Verify user record in User table
   - Verify staff record in Staff table with correct userId
   - Verify clinic record with correct ownerId

2. **Test Authentication**:
   - Login with created user
   - Verify token contains both userId and staffId
   - Verify protected routes work correctly

3. **Test Permissions**:
   - Verify admin users can access all routes
   - Verify staff permissions work correctly
   - Verify clinic access restrictions work

## Next Steps

After implementing these changes:

1. Update any remaining code that assumes the old structure
2. Add comprehensive tests for the new authentication flow
3. Update documentation to reflect the new structure
4. Consider adding refresh token functionality for better security
