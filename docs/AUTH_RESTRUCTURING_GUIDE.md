# Authentication System Restructuring Guide

This guide outlines the steps to restructure the authentication system in the VetCare SaaS application to make the User table the main table, with Staff borrowing userId from it. The system will only allow clinic owners and staff to sign up and log in.

## Overview of Changes

1. **Database Structure**:
   - User table becomes the main authentication table
   - Staff table references User table via userId
   - Remove duplicate authentication fields from Staff table

2. **Authentication Flow**:
   - All users are created in the User table first
   - Staff records are created with a reference to the User
   - JWT tokens include both userId and staffId when applicable

3. **Middleware Updates**:
   - Update auth middleware to handle the new token structure
   - Modify permission checks to work with the new relationship

## Step-by-Step Implementation

### 1. Update Staff Model

```javascript
// vet-care-systems/models/staff.model.js
const staffSchema = new mongoose.Schema({
    // Reference to User model (main table)
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, "User ID is required"],
        unique: true
    },

    // Remove duplicate fields (email, password, firstName, etc.)
    // Keep only staff-specific fields

    // Primary clinic where the staff member works
    clinicId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Clinic',
        required: true
    },
    // ... other fields
});
```

### 2. Update Clinic Model

```javascript
// vet-care-systems/models/clinic.model.js
const clinicSchema = new mongoose.Schema({
    // ... other fields
    ownerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        // Make it conditionally required
        required: [function() {
            // Skip validation during initial creation
            return !this.isNew;
        }, "Owner ID is required"],
    },
    // ... other fields
});
```

### 3. Update Signup Controller

```javascript
// vet-care-systems/controllers/auth.controller.js
export const signUp = async (req, res, next) => {
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        // Extract user data from request
        const {
            firstName, middleName, lastName, email, phoneNumber,
            password, address, dob, is_clinic_owner, clinic_data,
            // Staff-specific fields
            roleId, jobTitle, employmentDate, salary, emergencyContact, schedule
        } = req.body;

        // Check if user exists in User table
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return sendResponse(res, 409, false, "User already exists");
        }

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Create user in User table
        const [newUser] = await User.create(
            [{
                firstName, middleName, lastName, email, phoneNumber,
                password: hashedPassword, address, dob, status: 1,
                role: is_clinic_owner ? 'clinic_owner' : 'staff'
            }],
            { session }
        );

        // Create clinic if user is a clinic owner
        let clinicId = null;
        if (is_clinic_owner && clinic_data) {
            const [newClinic] = await Clinic.create([{
                clinicName: clinic_data.name,
                ownerId: newUser._id,
                phoneNumber: clinic_data.phoneNumber,
                email: clinic_data.email,
                address: clinic_data.address,
                status: 1
            }], { session });

            clinicId = newClinic._id;
        }

        // Create staff record linked to the user
        const [newStaff] = await Staff.create(
            [{
                userId: newUser._id,
                clinicId: clinicId,
                primaryClinicId: clinicId,
                isClinicOwner: is_clinic_owner,
                roleId: roleId || (is_clinic_owner ? 1 : 2),
                jobTitle: jobTitle || (is_clinic_owner ? 'Clinic Owner' : 'Staff'),
                employmentDate: employmentDate || new Date(),
                salary: salary || 0,
                emergencyContact: emergencyContact,
                schedule: schedule,
                status: 1
            }],
            { session }
        );

        // Generate token with both IDs
        const token = jwt.sign(
            {
                userId: newUser._id,
                staffId: newStaff._id
            },
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        // Prepare response
        const responseData = {
            token,
            user: { ...newUser._doc, password: undefined },
            staff: newStaff
        };

        await session.commitTransaction();
        return sendResponse(res, 201, true, "User created successfully", responseData);
    } catch (error) {
        await session.abortTransaction();
        return sendResponse(res, 500, false, error.message);
    } finally {
        session.endSession();
    }
};
```

### 4. Update Signin Controller

```javascript
// vet-care-systems/controllers/auth.controller.js
export const signIn = async (req, res) => {
    try {
        const { email, password } = req.body;

        // Find user in User table
        const user = await User.findOne({ email })
            .select('firstName middleName lastName email phoneNumber password status lastLogin loginCount role')
            .lean();

        if (!user) {
            return sendResponse(res, 404, false, "User not found");
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return sendResponse(res, 401, false, "Invalid credentials");
        }

        // Update login tracking
        await User.updateOne(
            { _id: user._id },
            {
                $set: { lastLogin: new Date() },
                $inc: { loginCount: 1 }
            }
        );

        // Remove password from response
        const userWithoutPassword = { ...user };
        delete userWithoutPassword.password;

        // Prepare response
        const responseData = {
            token: null,
            user: userWithoutPassword,
            staff: null,
            clinics: []
        };

        // Handle admin login
        if (user.role === 'admin' || user.role === 'super_admin') {
            const token = jwt.sign(
                { userId: user._id, isAdmin: true },
                JWT_SECRET,
                { expiresIn: JWT_EXPIRES_IN }
            );
            responseData.token = token;

            // Get all clinics for admin
            responseData.clinics = await Clinic.find({ status: 1 })
                .select('clinicName address phoneNumber email status')
                .lean();
        }
        // Handle staff login
        else {
            // Find staff record
            const staff = await Staff.findOne({ userId: user._id }).lean();
            if (!staff) {
                return sendResponse(res, 404, false, "Staff record not found");
            }

            const token = jwt.sign(
                { userId: user._id, staffId: staff._id },
                JWT_SECRET,
                { expiresIn: JWT_EXPIRES_IN }
            );

            responseData.token = token;
            responseData.staff = staff;

            // Get clinics for staff
            if (staff.primaryClinicId) {
                const primaryClinic = await Clinic.findById(staff.primaryClinicId)
                    .select('clinicName address phoneNumber email status')
                    .lean();

                if (primaryClinic) {
                    responseData.clinics = [primaryClinic];
                }
            }
        }

        return sendResponse(res, 200, true, "Login successful", responseData);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};
```

### 5. Update Auth Middleware

```javascript
// vet-care-systems/middleware/auth.middleware.js
export const verifyToken = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];
        if (!token) {
            return sendResponse(res, 401, false, 'No token provided');
        }

        const decoded = jwt.verify(token, JWT_SECRET);

        // Find user in User table
        const user = await User.findById(decoded.userId).select('-password');
        if (!user) {
            return sendResponse(res, 401, false, 'Invalid token');
        }

        req.user = user;

        // Check if admin
        req.user.isAdmin = user.role === 'admin' || user.role === 'super_admin';

        // If staffId exists, load staff data
        if (decoded.staffId) {
            const staff = await Staff.findById(decoded.staffId);
            if (staff) {
                req.staff = staff;
                req.user.isStaff = true;
                req.user.isClinicOwner = staff.isClinicOwner;
            }
        }

        next();
    } catch (error) {
        // Handle token errors
        return sendResponse(res, 401, false, 'Authentication failed');
    }
};
```

### 6. Update Permission Middleware

```javascript
// vet-care-systems/middleware/auth.middleware.js
export const hasPermission = (requiredPermissions) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return sendResponse(res, 401, false, 'Authentication required');
            }

            // System admin users have all permissions
            if (req.user.isAdmin) {
                return next();
            }

            // Only staff can have specific permissions
            if (!req.staff) {
                return sendResponse(res, 403, false, 'Staff access required');
            }

            // Get the role permissions
            const role = await Role.findOne({ roleId: req.staff.roleId }).lean();
            if (!role) {
                return sendResponse(res, 403, false, 'Role not found');
            }

            // Check each required permission
            for (const permissionId of requiredPermissions) {
                // Check if permission is revoked
                if (req.staff.revokedPermissions &&
                    req.staff.revokedPermissions.includes(permissionId)) {
                    return sendResponse(res, 403, false, 'Insufficient permissions');
                }

                // Check if permission is in role or special permissions
                const hasPermission =
                    (role.permissions && role.permissions.includes(permissionId)) ||
                    (req.staff.specialPermissions &&
                     req.staff.specialPermissions.includes(permissionId));

                if (!hasPermission) {
                    return sendResponse(res, 403, false, 'Insufficient permissions');
                }
            }

            next();
        } catch (error) {
            return sendResponse(res, 500, false, error.message);
        }
    };
};
```

### 7. Update Clinic Access Middleware

```javascript
// vet-care-systems/middleware/auth.middleware.js
export const hasClinicAccess = (clinicIdParam = 'clinicId') => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return sendResponse(res, 401, false, 'Authentication required');
            }

            // Get the clinic ID from the request
            const clinicId = req.params[clinicIdParam] ||
                             req.body[clinicIdParam] ||
                             req.query[clinicIdParam];

            if (!clinicId) {
                return sendResponse(res, 400, false, 'Clinic ID is required');
            }

            // System admin users have access to all clinics
            if (req.user.isAdmin) {
                return next();
            }

            // For staff users, check clinic access
            if (req.staff) {
                // Check if this is the primary clinic
                const isPrimaryClinic = req.staff.primaryClinicId &&
                    req.staff.primaryClinicId.toString() === clinicId.toString();

                // Check if this is in additional clinics
                const isInAdditionalClinics = req.staff.additionalClinics &&
                    req.staff.additionalClinics.some(id =>
                        id.toString() === clinicId.toString());

                if (!isPrimaryClinic && !isInAdditionalClinics) {
                    return sendResponse(res, 403, false,
                        'You do not have access to this clinic');
                }

                // Update the staff's clinic activity
                await Staff.findByIdAndUpdate(
                    req.staff._id,
                    {
                        $push: {
                            clinicActivity: {
                                clinicId: clinicId,
                                lastActive: new Date(),
                                activityCount: 1
                            }
                        }
                    }
                );

                return next();
            }

            // If we get here, the user is not a staff member or admin
            return sendResponse(res, 403, false, 'Staff or admin access required');
        } catch (error) {
            return sendResponse(res, 500, false, error.message);
        }
    };
};
```

## Frontend Changes

### 1. Update API Service

```typescript
// lovable-vetcare/src/services/api.ts
import axios from 'axios';
import { useAuthStore } from '@/store';

// Create axios instance
const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    headers: { 'Content-Type': 'application/json' }
});

// Add auth token to requests
apiClient.interceptors.request.use(config => {
    const token = useAuthStore.getState().token;
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Handle auth errors
apiClient.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 401) {
            useAuthStore.getState().logout();
        }
        return Promise.reject(error);
    }
);

export const api = {
    get: (url, config) => apiClient.get(url, config),
    post: (url, data, config) => apiClient.post(url, data, config),
    put: (url, data, config) => apiClient.put(url, data, config),
    delete: (url, config) => apiClient.delete(url, config)
};
```

### 2. Centralize Auth Store

```typescript
// lovable-vetcare/src/store/auth.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { api } from '@/services/api';

export const useAuthStore = create(
    persist(
        (set, get) => ({
            isAuthenticated: false,
            user: null,
            staff: null,
            token: null,
            clinics: [],
            currentClinic: null,

            login: async (email, password) => {
                try {
                    const response = await api.post('/auth/sign-in', { email, password });

                    if (response.success) {
                        set({
                            isAuthenticated: true,
                            user: response.data.user,
                            staff: response.data.staff || null,
                            token: response.data.token,
                            clinics: response.data.clinics || [],
                            currentClinic: response.data.clinics?.length > 0 ?
                                response.data.clinics[0] : null
                        });
                    }

                    return response;
                } catch (error) {
                    console.error('Login error:', error);
                    return {
                        success: false,
                        message: error.message || 'Login failed'
                    };
                }
            },

            logout: () => {
                set({
                    isAuthenticated: false,
                    user: null,
                    staff: null,
                    token: null,
                    clinics: [],
                    currentClinic: null
                });

                // Clear localStorage to ensure all persisted auth data is removed
                localStorage.removeItem('auth-storage');

                // Redirect to login page
                window.location.href = '/login';
            },

            getToken: () => get().token,

            updateUser: (updatedUser) => {
                set({ user: { ...get().user, ...updatedUser } });
            },

            updateStaff: (updatedStaff) => {
                set({ staff: { ...get().staff, ...updatedStaff } });
            }
        }),
        { name: 'auth-storage' }
    )
);

// Export a hook for components to use
export const useAuth = () => {
    const {
        isAuthenticated, user, token, staff, clinics, currentClinic,
        login, logout, getToken, updateUser, updateStaff
    } = useAuthStore();

    return {
        isAuthenticated, user, token, staff, clinics, currentClinic,
        login, logout, getToken, updateUser, updateStaff
    };
};
```

## Testing the Changes

1. **Test User Creation**:
   - Create a new clinic owner
   - Verify user record in User table
   - Verify staff record in Staff table with correct userId
   - Verify clinic record with correct ownerId

2. **Test Authentication**:
   - Login with created user
   - Verify token contains both userId and staffId
   - Verify protected routes work correctly

3. **Test Permissions**:
   - Verify admin users can access all routes
   - Verify staff permissions work correctly
   - Verify clinic access restrictions work

## Next Steps

After implementing these changes:

1. Update any remaining code that assumes the old structure
2. Add comprehensive tests for the new authentication flow
3. Update documentation to reflect the new structure
4. Consider adding refresh token functionality for better security

## Conclusion

This restructuring makes the authentication system more maintainable and follows better database design principles by:

1. Centralizing user authentication in the User table
2. Eliminating duplicate data between User and Staff tables
3. Creating a clear relationship between users and their roles
4. Simplifying token management and permission checks

The new structure also makes it easier to add new user types in the future if needed, while maintaining a consistent authentication flow.
