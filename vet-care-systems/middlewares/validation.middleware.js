/**
 * Input Validation Middleware using Zod
 * 
 * This middleware provides comprehensive input validation for API endpoints
 * using Zod schemas for type safety and data integrity.
 */

import { z } from 'zod';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Generic validation middleware factory
 * @param {Object} schemas - Object containing validation schemas for body, params, query
 * @returns {Function} Express middleware function
 */
export const validate = (schemas) => {
    return async (req, res, next) => {
        try {
            // Validate request body
            if (schemas.body) {
                req.body = await schemas.body.parseAsync(req.body);
            }

            // Validate request parameters
            if (schemas.params) {
                req.params = await schemas.params.parseAsync(req.params);
            }

            // Validate query parameters
            if (schemas.query) {
                req.query = await schemas.query.parseAsync(req.query);
            }

            next();
        } catch (error) {
            if (error instanceof z.ZodError) {
                const validationErrors = error.errors.map(err => ({
                    field: err.path.join('.'),
                    message: err.message,
                    code: err.code
                }));

                return sendResponse(res, 400, false, 'Validation failed', {
                    errors: validationErrors
                });
            }

            // Handle other validation errors
            return sendResponse(res, 400, false, 'Invalid input data', {
                error: error.message
            });
        }
    };
};

/**
 * Common validation schemas
 */

// Base schemas for common data types
export const commonSchemas = {
    // Numeric ID validation
    numericId: z.coerce.number().int().positive().min(1000),
    
    // Email validation
    email: z.string().email().toLowerCase().trim(),
    
    // Phone number validation
    phoneNumber: z.string().regex(/^\+?[\d\s\-\(\)]{10,14}$/, 'Invalid phone number format'),
    
    // Name validation
    name: z.string().trim().min(2).max(55),
    
    // Password validation
    password: z.string().min(6).max(128),
    
    // Date validation
    date: z.coerce.date(),
    
    // Status validation
    status: z.enum(['0', '1']).transform(val => parseInt(val)),
    
    // Currency validation
    currency: z.enum(['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED']),
    
    // Pagination
    pagination: z.object({
        page: z.coerce.number().int().positive().default(1),
        limit: z.coerce.number().int().positive().max(100).default(10),
        sortBy: z.string().optional(),
        sortOrder: z.enum(['asc', 'desc']).default('desc')
    })
};

/**
 * Authentication schemas
 */
export const authSchemas = {
    login: z.object({
        email: commonSchemas.email,
        password: z.string().min(1, 'Password is required')
    }),
    
    register: z.object({
        email: commonSchemas.email,
        password: commonSchemas.password,
        firstName: commonSchemas.name,
        lastName: commonSchemas.name,
        middleName: commonSchemas.name.optional(),
        phoneNumber: commonSchemas.phoneNumber,
        address: z.string().optional(),
        dob: commonSchemas.date.optional()
    }),
    
    changePassword: z.object({
        currentPassword: z.string().min(1, 'Current password is required'),
        newPassword: commonSchemas.password,
        confirmPassword: z.string()
    }).refine(data => data.newPassword === data.confirmPassword, {
        message: "Passwords don't match",
        path: ["confirmPassword"]
    })
};

/**
 * User management schemas
 */
export const userSchemas = {
    createUser: authSchemas.register.extend({
        roleId: commonSchemas.numericId
    }),
    
    updateUser: z.object({
        firstName: commonSchemas.name.optional(),
        lastName: commonSchemas.name.optional(),
        middleName: commonSchemas.name.optional(),
        phoneNumber: commonSchemas.phoneNumber.optional(),
        address: z.string().optional(),
        dob: commonSchemas.date.optional(),
        status: commonSchemas.status.optional()
    }),
    
    userParams: z.object({
        userId: commonSchemas.numericId
    })
};

/**
 * Clinic management schemas
 */
export const clinicSchemas = {
    createClinic: z.object({
        clinicName: z.string().trim().min(2).max(100),
        phoneNumber: commonSchemas.phoneNumber,
        email: commonSchemas.email,
        address: z.string().trim().min(5).max(255),
        location: z.object({
            coordinates: z.array(z.number()).length(2).optional()
        }).optional()
    }),
    
    updateClinic: z.object({
        clinicName: z.string().trim().min(2).max(100).optional(),
        phoneNumber: commonSchemas.phoneNumber.optional(),
        email: commonSchemas.email.optional(),
        address: z.string().trim().min(5).max(255).optional(),
        status: commonSchemas.status.optional(),
        managerId: commonSchemas.numericId.optional()
    }),
    
    clinicParams: z.object({
        clinicId: commonSchemas.numericId
    })
};

/**
 * Pet management schemas
 */
export const petSchemas = {
    createPet: z.object({
        petName: z.string().trim().min(1).max(50),
        speciesId: commonSchemas.numericId,
        breedId: commonSchemas.numericId,
        gender: z.enum(['male', 'female']),
        dateOfBirth: commonSchemas.date.optional(),
        weight: z.number().positive().optional(),
        color: z.string().trim().max(50).optional(),
        microchipNumber: z.string().trim().max(50).optional(),
        clientId: commonSchemas.numericId,
        clinicId: commonSchemas.numericId
    }),
    
    updatePet: z.object({
        petName: z.string().trim().min(1).max(50).optional(),
        speciesId: commonSchemas.numericId.optional(),
        breedId: commonSchemas.numericId.optional(),
        gender: z.enum(['male', 'female']).optional(),
        dateOfBirth: commonSchemas.date.optional(),
        weight: z.number().positive().optional(),
        color: z.string().trim().max(50).optional(),
        microchipNumber: z.string().trim().max(50).optional(),
        status: commonSchemas.status.optional()
    }),
    
    petParams: z.object({
        petId: commonSchemas.numericId
    })
};

/**
 * Appointment schemas
 */
export const appointmentSchemas = {
    createAppointment: z.object({
        petId: commonSchemas.numericId,
        clientId: commonSchemas.numericId,
        clinicId: commonSchemas.numericId,
        appointmentDate: commonSchemas.date,
        estimatedDuration: z.number().int().positive().default(30),
        priority: z.enum(['low', 'normal', 'high', 'emergency']).default('normal'),
        reason: z.string().trim().min(5).max(500),
        categories: z.array(z.object({
            appointmentCategoryId: commonSchemas.numericId,
            notes: z.string().optional()
        })).min(1, 'At least one category is required')
    }),
    
    updateAppointment: z.object({
        appointmentDate: commonSchemas.date.optional(),
        estimatedDuration: z.number().int().positive().optional(),
        priority: z.enum(['low', 'normal', 'high', 'emergency']).optional(),
        status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled', 'no_show']).optional(),
        reason: z.string().trim().min(5).max(500).optional()
    }),
    
    appointmentParams: z.object({
        appointmentId: commonSchemas.numericId
    })
};

/**
 * Query parameter schemas
 */
export const querySchemas = {
    // Common pagination and filtering
    listQuery: commonSchemas.pagination.extend({
        search: z.string().optional(),
        status: z.enum(['0', '1', 'all']).default('all'),
        clinicId: commonSchemas.numericId.optional()
    }),
    
    // Date range queries
    dateRangeQuery: z.object({
        startDate: commonSchemas.date.optional(),
        endDate: commonSchemas.date.optional()
    }).refine(data => {
        if (data.startDate && data.endDate) {
            return data.startDate <= data.endDate;
        }
        return true;
    }, {
        message: "Start date must be before or equal to end date",
        path: ["endDate"]
    }),
    
    // Appointment filtering
    appointmentQuery: commonSchemas.pagination.extend({
        petId: commonSchemas.numericId.optional(),
        clientId: commonSchemas.numericId.optional(),
        clinicId: commonSchemas.numericId.optional(),
        status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled', 'no_show', 'all']).default('all'),
        priority: z.enum(['low', 'normal', 'high', 'emergency', 'all']).default('all'),
        startDate: commonSchemas.date.optional(),
        endDate: commonSchemas.date.optional()
    })
};

/**
 * Validation middleware for specific routes
 */
export const validationMiddleware = {
    // Authentication
    validateLogin: validate({ body: authSchemas.login }),
    validateRegister: validate({ body: authSchemas.register }),
    validateChangePassword: validate({ body: authSchemas.changePassword }),
    
    // User management
    validateCreateUser: validate({ body: userSchemas.createUser }),
    validateUpdateUser: validate({ body: userSchemas.updateUser, params: userSchemas.userParams }),
    validateUserParams: validate({ params: userSchemas.userParams }),
    
    // Clinic management
    validateCreateClinic: validate({ body: clinicSchemas.createClinic }),
    validateUpdateClinic: validate({ body: clinicSchemas.updateClinic, params: clinicSchemas.clinicParams }),
    validateClinicParams: validate({ params: clinicSchemas.clinicParams }),
    
    // Pet management
    validateCreatePet: validate({ body: petSchemas.createPet }),
    validateUpdatePet: validate({ body: petSchemas.updatePet, params: petSchemas.petParams }),
    validatePetParams: validate({ params: petSchemas.petParams }),
    
    // Appointment management
    validateCreateAppointment: validate({ body: appointmentSchemas.createAppointment }),
    validateUpdateAppointment: validate({ body: appointmentSchemas.updateAppointment, params: appointmentSchemas.appointmentParams }),
    validateAppointmentParams: validate({ params: appointmentSchemas.appointmentParams }),
    
    // Query validation
    validateListQuery: validate({ query: querySchemas.listQuery }),
    validateDateRangeQuery: validate({ query: querySchemas.dateRangeQuery }),
    validateAppointmentQuery: validate({ query: querySchemas.appointmentQuery })
};

export default validationMiddleware;
