/**
 * Test JSON Queries for Veterinary System
 *
 * This script demonstrates how JSON storage can reduce data complexity
 * while maintaining powerful query capabilities.
 */

import mongoose from 'mongoose';
import OptimizedAppointment from './models/optimizedAppointment.model.js';

// MongoDB connection
const MONGODB_URI = "mongodb+srv://kabiujm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function connectDB() {
    try {
        await mongoose.connect(MONGODB_URI);
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ MongoDB connection error:', error);
        process.exit(1);
    }
}

async function createSampleData() {
    console.log('\n📝 Creating sample optimized appointments...');

    const sampleAppointments = [
        {
            petId: 2001,
            clinicId: 1001,
            staffId: 3001,
            clientId: 4001,
            appointmentDate: new Date('2024-01-15T10:00:00Z'),
            priority: 'normal',
            status: 'completed',
            appointmentData: {
                appointmentTypes: [{ appointmentTypeId: 1, name: 'Consultation', duration: 30 }],
                reason: 'Annual checkup',
                notes: 'Routine examination',
                isAfterHours: false
            },
            petData: {
                petName: 'Buddy',
                species: 'Dog',
                breed: 'Golden Retriever',
                age: 5,
                weight: 25.5,
                allergies: ['peanuts', 'chicken'],
                chronicConditions: ['hip dysplasia']
            },
            clientData: {
                firstName: 'John',
                lastName: 'Doe',
                phone: '+**********',
                email: '<EMAIL>',
                address: '123 Main St'
            },
            staffData: {
                firstName: 'Dr. Sarah',
                lastName: 'Johnson',
                specialization: 'Small Animals',
                licenseNumber: 'VET12345'
            },
            servicesData: [
                { serviceId: 1001, name: 'Physical Examination', category: 'examination', cost: 50, status: 'completed' },
                { serviceId: 1002, name: 'Vaccination', category: 'prevention', cost: 30, status: 'completed' }
            ],
            medicalData: {
                vitalSigns: { temperature: 101.5, heartRate: 120, weight: 25.5 },
                diagnosis: 'Healthy',
                treatment: 'Routine care',
                medications: [{ name: 'Vitamins', dosage: '1 tablet daily' }]
            },
            billingData: {
                baseAmount: 80,
                afterHoursCharge: 0,
                discounts: [],
                paymentStatus: 'paid'
            }
        },
        {
            petId: 2002,
            clinicId: 1001,
            staffId: 3002,
            clientId: 4002,
            appointmentDate: new Date('2024-01-16T14:30:00Z'),
            priority: 'high',
            status: 'completed',
            appointmentData: {
                appointmentTypes: [{ appointmentTypeId: 2, name: 'Emergency', duration: 60 }],
                reason: 'Injury from accident',
                notes: 'Hit by car, leg injury',
                isAfterHours: false
            },
            petData: {
                petName: 'Luna',
                species: 'Cat',
                breed: 'Persian',
                age: 3,
                weight: 4.2,
                allergies: [],
                chronicConditions: []
            },
            clientData: {
                firstName: 'Jane',
                lastName: 'Smith',
                phone: '+**********',
                email: '<EMAIL>',
                address: '456 Oak Ave'
            },
            staffData: {
                firstName: 'Dr. Michael',
                lastName: 'Brown',
                specialization: 'Emergency Medicine',
                licenseNumber: 'VET67890'
            },
            servicesData: [
                { serviceId: 1003, name: 'X-Ray', category: 'imaging', cost: 120, status: 'completed' },
                { serviceId: 1004, name: 'Surgery', category: 'surgery', cost: 500, status: 'completed' },
                { serviceId: 1005, name: 'Pain Management', category: 'treatment', cost: 80, status: 'completed' }
            ],
            medicalData: {
                vitalSigns: { temperature: 102.1, heartRate: 140, weight: 4.2 },
                diagnosis: 'Fractured leg',
                treatment: 'Surgical repair',
                medications: [
                    { name: 'Pain medication', dosage: '0.5ml twice daily' },
                    { name: 'Antibiotics', dosage: '1 tablet daily for 7 days' }
                ]
            },
            billingData: {
                baseAmount: 700,
                afterHoursCharge: 0,
                discounts: [{ type: 'emergency', amount: 50 }],
                paymentStatus: 'pending'
            }
        }
    ];

    try {
        // Clear existing data
        await OptimizedAppointment.deleteMany({});

        // Insert sample data
        const created = await OptimizedAppointment.insertMany(sampleAppointments);
        console.log(`✅ Created ${created.length} sample appointments`);
        return created;
    } catch (error) {
        console.error('❌ Error creating sample data:', error);
    }
}

async function demonstrateQueries() {
    console.log('\n🔍 Demonstrating JSON Query Capabilities...\n');

    try {
        // 1. Basic indexed field queries (FAST)
        console.log('1️⃣ Basic indexed queries:');
        console.time('Basic Query');
        const basicQuery = await OptimizedAppointment.find({
            status: 'completed',
            priority: 'high'
        });
        console.timeEnd('Basic Query');
        console.log(`   Found ${basicQuery.length} high-priority completed appointments\n`);

        // 2. JSON field queries
        console.log('2️⃣ JSON field queries:');
        console.time('JSON Query');
        const jsonQuery = await OptimizedAppointment.find({
            'petData.species': 'Dog',
            'petData.allergies': { $exists: true, $ne: [] }
        });
        console.timeEnd('JSON Query');
        console.log(`   Found ${jsonQuery.length} dogs with allergies\n`);

        // 3. Array queries within JSON
        console.log('3️⃣ Array queries within JSON:');
        console.time('Array Query');
        const arrayQuery = await OptimizedAppointment.find({
            'servicesData.category': 'surgery'
        });
        console.timeEnd('Array Query');
        console.log(`   Found ${arrayQuery.length} appointments with surgery\n`);

        // 4. Complex nested queries
        console.log('4️⃣ Complex nested queries:');
        console.time('Complex Query');
        const complexQuery = await OptimizedAppointment.find({
            $and: [
                { 'petData.weight': { $gte: 20 } },
                { 'medicalData.vitalSigns.temperature': { $gt: 101 } },
                { 'billingData.paymentStatus': 'paid' }
            ]
        });
        console.timeEnd('Complex Query');
        console.log(`   Found ${complexQuery.length} large pets with fever and paid bills\n`);

        // 5. Text search
        console.log('5️⃣ Full-text search:');
        console.time('Text Search');
        const textSearch = await OptimizedAppointment.find({
            $text: { $search: 'Golden Retriever vaccination' }
        });
        console.timeEnd('Text Search');
        console.log(`   Found ${textSearch.length} appointments matching text search\n`);

        // 6. Keyword search (indexed)
        console.log('6️⃣ Keyword search (indexed):');
        console.time('Keyword Search');
        const keywordSearch = await OptimizedAppointment.find({
            keywords: { $in: ['dog', 'vaccination'] }
        });
        console.timeEnd('Keyword Search');
        console.log(`   Found ${keywordSearch.length} appointments with keywords\n`);

        // 7. Aggregation with JSON data
        console.log('7️⃣ Aggregation with JSON data:');
        console.time('Aggregation');
        const aggregation = await OptimizedAppointment.aggregate([
            { $match: { status: 'completed' } },
            {
                $group: {
                    _id: '$petData.species',
                    count: { $sum: 1 },
                    avgCost: { $avg: '$totalCost' },
                    totalRevenue: { $sum: '$totalCost' }
                }
            },
            { $sort: { totalRevenue: -1 } }
        ]);
        console.timeEnd('Aggregation');
        console.log('   Species revenue analysis:');
        aggregation.forEach(item => {
            console.log(`   - ${item._id}: ${item.count} appointments, $${item.totalRevenue} revenue, $${item.avgCost.toFixed(2)} avg`);
        });
        console.log();

        // 8. Service category analysis
        console.log('8️⃣ Service category analysis:');
        console.time('Service Analysis');
        const serviceAnalysis = await OptimizedAppointment.aggregate([
            { $unwind: '$servicesData' },
            {
                $group: {
                    _id: '$servicesData.category',
                    count: { $sum: 1 },
                    totalRevenue: { $sum: '$servicesData.cost' },
                    avgCost: { $avg: '$servicesData.cost' }
                }
            },
            { $sort: { totalRevenue: -1 } }
        ]);
        console.timeEnd('Service Analysis');
        console.log('   Service category performance:');
        serviceAnalysis.forEach(item => {
            console.log(`   - ${item._id}: ${item.count} services, $${item.totalRevenue} revenue, $${item.avgCost.toFixed(2)} avg`);
        });

    } catch (error) {
        console.error('❌ Query error:', error);
    }
}

async function demonstratePerformanceBenefits() {
    console.log('\n⚡ Performance Benefits of JSON Storage:\n');

    console.log('✅ ADVANTAGES:');
    console.log('   • Single query instead of multiple JOINs/populates');
    console.log('   • Reduced network round trips');
    console.log('   • All related data in one document');
    console.log('   • Flexible schema for varying data structures');
    console.log('   • Fast keyword and category searches');
    console.log('   • Efficient aggregation pipelines');
    console.log('   • Reduced database complexity');

    console.log('\n⚠️  CONSIDERATIONS:');
    console.log('   • Document size limits (16MB in MongoDB)');
    console.log('   • Index essential fields for fast queries');
    console.log('   • Extract searchable keywords');
    console.log('   • Use text indexes for full-text search');
    console.log('   • Consider data consistency requirements');

    console.log('\n💡 BEST PRACTICES:');
    console.log('   • Index frequently queried fields');
    console.log('   • Extract keywords and categories');
    console.log('   • Use pre-save middleware for data processing');
    console.log('   • Implement proper validation');
    console.log('   • Monitor document sizes');
    console.log('   • Use aggregation for complex analytics');
}

async function main() {
    console.log('🚀 JSON Query Demonstration for Veterinary System');
    console.log('================================================\n');

    await connectDB();
    await createSampleData();
    await demonstrateQueries();
    await demonstratePerformanceBenefits();

    console.log('\n✅ Demonstration completed!');
    console.log('\n📊 Summary: JSON storage can significantly reduce data complexity');
    console.log('   while maintaining excellent query performance when properly indexed.');

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
}

// Run the demonstration
main().catch(console.error);
