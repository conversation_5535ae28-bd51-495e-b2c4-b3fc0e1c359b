import mongoose from 'mongoose'
import {DB_URI, NODE_ENV, PORT} from "../config/env.js";

if(!DB_URI) {
    throw new Error("MongoDB URI doesn't exist: check .env<development/production>.local");
}

const connectToDatabase = async () => {
    try {
        console.log(`MongoDB connection started on port ${PORT}`);

        // Enhanced connection options
        const options = {
            maxPoolSize: 10, // Maintain up to 10 socket connections
            serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
            socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
            bufferMaxEntries: 0, // Disable mongoose buffering
            bufferCommands: false, // Disable mongoose buffering
            maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
            family: 4 // Use IPv4, skip trying IPv6
        };

        await mongoose.connect(DB_URI, options);

        console.log(`✅ MongoDB Connected: IN ${NODE_ENV} MODE`);

        // Handle connection events
        mongoose.connection.on('error', (err) => {
            console.error('❌ MongoDB connection error:', err);
        });

        mongoose.connection.on('disconnected', () => {
            console.warn('⚠️ MongoDB disconnected');
        });

        mongoose.connection.on('reconnected', () => {
            console.log('🔄 MongoDB reconnected');
        });

        // Graceful shutdown
        process.on('SIGINT', async () => {
            try {
                await mongoose.connection.close();
                console.log('📴 MongoDB connection closed through app termination');
                process.exit(0);
            } catch (err) {
                console.error('Error closing MongoDB connection:', err);
                process.exit(1);
            }
        });

    } catch(err) {
        console.error("❌ Error connecting to database:", err);
        process.exit(1);
    }
}

export default connectToDatabase;