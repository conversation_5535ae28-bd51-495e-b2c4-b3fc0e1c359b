import mongoose from 'mongoose'
import {DB_URI, NODE_ENV, PORT} from "../config/env.js";

if(!DB_URI) {
    throw new Error("MongoDB URI doesn't exist: check .env<development/production>.local");
}

const connectToDatabase = async () => {
    try {
        console.log(`MongoDB connection started on port ${PORT}`);

        // Basic connection without problematic options
        await mongoose.connect(DB_URI);

        console.log(`✅ MongoDB Connected: IN ${NODE_ENV} MODE`);

        // Handle connection events
        mongoose.connection.on('error', (err) => {
            console.error('❌ MongoDB connection error:', err);
        });

        mongoose.connection.on('disconnected', () => {
            console.warn('⚠️ MongoDB disconnected');
        });

        mongoose.connection.on('reconnected', () => {
            console.log('🔄 MongoDB reconnected');
        });

        // Graceful shutdown
        process.on('SIGINT', async () => {
            try {
                await mongoose.connection.close();
                console.log('📴 MongoDB connection closed through app termination');
                process.exit(0);
            } catch (err) {
                console.error('Error closing MongoDB connection:', err);
                process.exit(1);
            }
        });

    } catch(err) {
        console.error("❌ Error connecting to database:", err);
        process.exit(1);
    }
}

export default connectToDatabase;