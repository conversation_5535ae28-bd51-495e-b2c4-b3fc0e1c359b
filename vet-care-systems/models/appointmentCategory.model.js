import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const appointmentCategorySchema = new mongoose.Schema({
    // Auto-increment ID
    appointmentCategoryId: {
        type: Number,
        index: true,
        unique: true,
    },
    name: {
        type: String,
        required: [true, "Appointment category name is required"],
        unique: true,
        trim: true,
        maxLength: 100,
    },
    description: {
        type: String,
        trim: true,
        maxLength: 500,
    },
    icon: {
        type: String,
        trim: true,
    },
    color: {
        type: String,
        trim: true,
        default: '#3B82F6', // Default blue color
    },
    displayOrder: {
        type: Number,
        default: 0,
        index: true,
    },
    isActive: {
        type: Boolean,
        default: true,
        index: true,
    },
    // Remove clinicId - categories are now global
    // Clinic-specific settings will be in ClinicCategorySettings
    // Default staff roles that typically handle this category
    defaultStaffRoles: [{
        type: String,
        enum: ['veterinarian', 'vet_tech', 'groomer', 'receptionist', 'lab_tech', 'assistant']
    }],
    // Estimated time for appointments in this category
    estimatedDuration: {
        type: Number, // in minutes
        default: 30,
    },
    // Whether this category requires special equipment
    requiresEquipment: {
        type: Boolean,
        default: false,
    },
    // Whether this category requires special qualifications
    requiresQualification: {
        type: Boolean,
        default: false,
    },
    // Default charge for this category
    defaultCharge: {
        type: Number,
        default: 0,
        min: 0
    },
    // Currency for the default charge
    currency: {
        type: String,
        default: 'KES',
        enum: ['KES', 'USD', 'EUR', 'GBP']
    },
    // Default discount percentage for this category
    defaultDiscountPercentage: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    createdBy: {
        type: Number,
        required: true,
        index: true,
    },
    modifiedBy: {
        type: Number,
        index: true,
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Add auto-increment plugin
appointmentCategorySchema.plugin(AutoIncrement, { inc_field: 'appointmentCategoryId', start_seq: 1001 });
appointmentCategorySchema.index({ appointmentCategoryId: 1 }, { unique: true });

// Indexes for efficient queries
appointmentCategorySchema.index({ name: 1 });
appointmentCategorySchema.index({ clinicId: 1, isActive: 1 });
appointmentCategorySchema.index({ isActive: 1, displayOrder: 1 });

// Virtual for services in this appointment category (services that belong to this category)
appointmentCategorySchema.virtual('services', {
    ref: 'CategoryService',
    localField: 'appointmentCategoryId',
    foreignField: 'appointmentCategoryId'
});

// Virtual for service count (services that belong to this category)
appointmentCategorySchema.virtual('serviceCount', {
    ref: 'CategoryService',
    localField: 'appointmentCategoryId',
    foreignField: 'appointmentCategoryId',
    count: true
});

const AppointmentCategory = mongoose.model('AppointmentCategory', appointmentCategorySchema);

export default AppointmentCategory;
