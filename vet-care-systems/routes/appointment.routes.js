/**
 * New Appointment Routes with Service Categories
 */

import express from 'express';
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createAppointment,
    updateAppointment,
    getAllAppointments,
    getAppointmentById,
    getAppointmentCategories,
    getCategoryServices,
    addServiceToAppointment,
    completeService,
    updateAppointmentNotes,
    assignCategoriesToAppointment,
    deleteAppointmentCategory,
    deleteServiceFromAppointment,
    updateAppointmentCategory,
    updateServiceInAppointment,
    assignStaffToAppointment,
    updateAppointmentStatus,
    addServiceToAppointmentWorkflow,
    addTaskToAppointment,
    getCurrentAppointments,
    createFollowUpAppointment,
    scheduleFollowUpReminder,
    getAppointmentChain,
    getOverdueFollowUps,
    getUpcomingFollowUps,
    // getAppointmentTaskStatus, // Deprecated - use appointment status instead
    completeAppointmentCategory,
    generateAppointmentSummary,
    generateAIAppointmentNotes,
    generateMedicalRecord,
    addMedicationToService,
    removeMedicationFromService,
    cleanupAppointmentStaffData,
    getAppointmentWithCleanStaffData,
    migrateStaffAssignments,
    completeAppointment,
    // Enhanced appointment features
    checkAppointmentAvailability,
    lockTimeSlot,
    releaseTimeSlot,
    getDailySchedule,
    optimizeSchedule,
    addToWaitingList,
    getWaitingList,
    confirmWaitingListAppointment,
    cancelWaitingListEntry,
    createRecurringAppointment,
    getRecurringAppointments,
    updateRecurringAppointment,
    pauseRecurringAppointment,
    resumeRecurringAppointment,
    cancelRecurringAppointment
} from '../controllers/appointment.controller.js';
import { getAllAppointmentCategories } from '../controllers/appointmentCategory.controller.js';
import {
    generateAISuggestions,
    getAppointmentSuggestions,
    reviewSuggestion as reviewAISuggestion,
    implementSuggestion as implementAISuggestion
} from '../controllers/aiSuggestion.controller.js';

const router = express.Router();

console.log('🚀 Appointment routes loaded!');

// Apply authentication to all routes
router.use(verifyToken);

// Get appointment categories
router.get('/categories', getAppointmentCategories);

// Get services for a specific appointment category
router.get('/categories/:appointmentCategoryId/services', getCategoryServices);

// Get appointment types (categories) - legacy route
router.get('/types', getAllAppointmentCategories);

// Get current appointments (in progress)
router.get('/current', getCurrentAppointments);

// Get overdue follow-ups
router.get('/follow-ups/overdue', getOverdueFollowUps);

// Get upcoming follow-ups
router.get('/follow-ups/upcoming', getUpcomingFollowUps);

// Create new appointment
router.post('/', createAppointment);

// Get all appointments with filtering
router.get('/', getAllAppointments);

// Get single appointment by ID
router.get('/:appointmentId', getAppointmentById);

// Get appointment task status (deprecated - use appointment status instead)
// router.get('/:appointmentId/task-status', getAppointmentTaskStatus);

// Update appointment
router.put('/:appointmentId', updateAppointment);

// Get appointment chain (parent + follow-ups)
router.get('/:appointmentId/chain', getAppointmentChain);

// Create follow-up appointment
router.post('/:appointmentId/follow-up', createFollowUpAppointment);

// Schedule follow-up reminder
router.post('/:appointmentId/follow-up-reminder', scheduleFollowUpReminder);

// Add service to appointment
router.post('/:appointmentId/services', addServiceToAppointment);

// Complete a specific service
router.put('/:appointmentId/categories/:serviceCategoryId/services/:serviceId/complete', completeService);

// Complete an entire appointment category (for task status)
router.post('/:appointmentId/categories/:category/complete', completeAppointmentCategory);

// Generate appointment summary
router.post('/:appointmentId/generate-summary', generateAppointmentSummary);

// Generate AI appointment notes
router.post('/:appointmentId/generate-ai-notes', generateAIAppointmentNotes);

// Generate medical record from completed appointment
router.post('/:appointmentId/generate-medical-record', generateMedicalRecord);

// AI Suggestions routes
router.get('/:appointmentId/ai-suggestions', getAppointmentSuggestions);
router.post('/:appointmentId/ai-suggestions', generateAISuggestions);
router.put('/suggestions/:suggestionId/review', reviewAISuggestion);
router.put('/suggestions/:suggestionId/implement', implementAISuggestion);

// Medication management for services
router.post('/:appointmentId/categories/:appointmentCategoryId/services/:categoryServiceId/medications', addMedicationToService);
router.delete('/:appointmentId/categories/:appointmentCategoryId/services/:categoryServiceId/medications/:medicationIndex', removeMedicationFromService);

// Staff data cleanup and management
router.post('/cleanup-staff-data', cleanupAppointmentStaffData);
router.post('/:appointmentId/cleanup-staff-data', cleanupAppointmentStaffData);
router.get('/:appointmentId/clean-staff-data', getAppointmentWithCleanStaffData);

// Migrate staff assignments from service level to category level
router.post('/migrate-staff-assignments', migrateStaffAssignments);
router.post('/:appointmentId/migrate-staff-assignments', migrateStaffAssignments);

// Update appointment notes and recommendations
router.put('/:appointmentId/notes', updateAppointmentNotes);

// Assign categories to appointment with staff assignments
router.post('/:appointmentId/assign-categories', assignCategoriesToAppointment);

// Delete appointment category
router.delete('/:appointmentId/categories/:appointmentCategoryId', deleteAppointmentCategory);

// Delete service from appointment category
router.delete('/:appointmentId/categories/:appointmentCategoryId/services/:serviceId', deleteServiceFromAppointment);

// Update appointment category (for notes, etc.)
router.put('/:appointmentId/categories/:appointmentCategoryId', updateAppointmentCategory);

// Update service in appointment category (for notes, etc.)
router.put('/:appointmentId/categories/:appointmentCategoryId/services/:serviceId', updateServiceInAppointment);

// Assign staff to appointment category
router.post('/:appointmentId/assign-staff', assignStaffToAppointment);

// Update staff assignment for specific appointment category
router.put('/:appointmentId/categories/:categoryId/assign-staff', assignStaffToAppointment);

// Update appointment status and completion (legacy route)
router.put('/:appointmentId/status', updateAppointmentStatus);

// Complete entire appointment - use the completeAppointment function
router.put('/:appointmentId/complete', completeAppointment);

// Add service to appointment workflow
router.post('/:appointmentId/services', addServiceToAppointmentWorkflow);

// Add task to appointment
router.post('/:appointmentId/tasks', addTaskToAppointment);

// ========================================
// ENHANCED APPOINTMENT FEATURES (Phase 1)
// ========================================

// Appointment Availability & Conflict Detection
router.post('/check-availability', checkAppointmentAvailability);
router.post('/lock-slot', lockTimeSlot);
router.post('/release-slot', releaseTimeSlot);
router.get('/schedule/daily', getDailySchedule);
router.get('/schedule/optimize', optimizeSchedule);

// Waiting List Management
router.post('/waiting-list', addToWaitingList);
router.get('/waiting-list', getWaitingList);
router.post('/waiting-list/:waitingListId/confirm', confirmWaitingListAppointment);
router.delete('/waiting-list/:waitingListId', cancelWaitingListEntry);

// Recurring Appointments
router.post('/recurring', createRecurringAppointment);
router.get('/recurring', getRecurringAppointments);
router.put('/recurring/:recurringId', updateRecurringAppointment);
router.post('/recurring/:recurringId/pause', pauseRecurringAppointment);
router.post('/recurring/:recurringId/resume', resumeRecurringAppointment);
router.delete('/recurring/:recurringId', cancelRecurringAppointment);

export default router;