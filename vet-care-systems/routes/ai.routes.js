import express from 'express';
import {
    getAppointmentSuggestions,
    getMedicalRecommendations,
    triggerWorkflowAutomation,
    getClinicInsights
} from '../controllers/ai.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';
import { validateAIRequest } from '../middleware/validation.middleware.js';

const router = express.Router();

// All AI routes require authentication
router.use(verifyToken);

/**
 * AI Appointment Suggestions
 * POST /api/ai/appointment-suggestions
 * Get AI-powered appointment category suggestions based on symptoms
 */
router.post('/appointment-suggestions', 
    validateAIRequest('appointment_suggestion'),
    getAppointmentSuggestions
);

/**
 * AI Medical Recommendations
 * POST /api/ai/medical-recommendations
 * Get AI-powered medical recommendations for ongoing appointments
 */
router.post('/medical-recommendations',
    validateAIRequest('medical_recommendation'),
    getMedicalRecommendations
);

/**
 * AI Workflow Automation
 * POST /api/ai/workflow-automation
 * Trigger AI-powered workflow automation
 */
router.post('/workflow-automation',
    validateAIRequest('workflow_automation'),
    triggerWorkflowAutomation
);

/**
 * AI Clinic Insights
 * GET /api/ai/clinic-insights
 * Get AI-powered insights about clinic performance and patterns
 */
router.get('/clinic-insights',
    getClinicInsights
);

export default router;
