/**
 * Recurring Appointments Service
 * 
 * Handles creation and management of recurring appointments for regular check-ups,
 * vaccinations, and ongoing treatments.
 */

import mongoose from 'mongoose';
import { getNextSequence } from '../utils/counterManager.js';
import appointmentScheduler from './appointmentScheduler.service.js';
import notificationService from './notification.service.js';
import Appointment from '../models/appointment.model.js';

// Recurring Appointment Template Schema
const recurringAppointmentSchema = new mongoose.Schema({
    recurringId: {
        type: Number,
        unique: true
    },
    clientId: {
        type: Number,
        required: true,
        ref: 'Client'
    },
    petId: {
        type: Number,
        required: true,
        ref: 'Pet'
    },
    clinicId: {
        type: Number,
        required: true,
        ref: 'Clinic'
    },
    templateName: {
        type: String,
        required: true
    },
    recurrencePattern: {
        type: {
            type: String,
            enum: ['daily', 'weekly', 'monthly', 'yearly'],
            required: true
        },
        interval: {
            type: Number,
            default: 1 // Every 1 week, 2 months, etc.
        },
        daysOfWeek: [Number], // 0-6 (Sunday-Saturday) for weekly
        dayOfMonth: Number,   // 1-31 for monthly
        monthOfYear: Number   // 1-12 for yearly
    },
    appointmentTemplate: {
        estimatedDuration: {
            type: Number,
            default: 30
        },
        priority: {
            type: String,
            enum: ['low', 'normal', 'high', 'emergency'],
            default: 'normal'
        },
        reason: String,
        categories: [{
            appointmentCategoryId: Number,
            notes: String
        }],
        staffPreference: Number,
        preferredTimeSlots: [{
            startTime: String, // "09:00"
            endTime: String    // "17:00"
        }]
    },
    startDate: {
        type: Date,
        required: true
    },
    endDate: Date, // Optional end date
    maxOccurrences: Number, // Alternative to end date
    status: {
        type: String,
        enum: ['active', 'paused', 'completed', 'cancelled'],
        default: 'active'
    },
    createdAppointments: [{
        appointmentId: Number,
        scheduledDate: Date,
        status: String
    }],
    nextScheduledDate: Date,
    lastProcessedDate: Date,
    notificationSettings: {
        advanceNotice: {
            type: Number,
            default: 7 // Days before appointment
        },
        reminderChannels: [{
            type: String,
            enum: ['email', 'sms', 'app']
        }]
    }
}, {
    timestamps: true
});

// Auto-increment recurringId
recurringAppointmentSchema.pre('save', async function(next) {
    if (this.isNew) {
        this.recurringId = await getNextSequence('recurringAppointment');
    }
    next();
});

// Index for efficient querying
recurringAppointmentSchema.index({ clinicId: 1, status: 1 });
recurringAppointmentSchema.index({ nextScheduledDate: 1, status: 1 });
recurringAppointmentSchema.index({ clientId: 1, petId: 1 });

const RecurringAppointment = mongoose.model('RecurringAppointment', recurringAppointmentSchema);

class RecurringAppointmentsService {
    constructor() {
        this.processingInterval = 24 * 60 * 60 * 1000; // Check daily
        this.startPeriodicProcessing();
    }

    /**
     * Create recurring appointment template
     * @param {Object} recurringData - Recurring appointment data
     * @returns {Promise<Object>} Created recurring appointment
     */
    async createRecurringAppointment(recurringData) {
        try {
            const recurring = new RecurringAppointment(recurringData);
            
            // Calculate next scheduled date
            recurring.nextScheduledDate = this.calculateNextDate(
                recurring.startDate,
                recurring.recurrencePattern
            );

            await recurring.save();

            // Create first appointment if start date is today or in the past
            if (recurring.startDate <= new Date()) {
                await this.createNextAppointment(recurring);
            }

            console.log(`✅ Created recurring appointment: ${recurring.recurringId}`);
            return recurring;
        } catch (error) {
            console.error('Error creating recurring appointment:', error);
            throw error;
        }
    }

    /**
     * Process all active recurring appointments
     */
    async processRecurringAppointments() {
        try {
            const now = new Date();
            const lookAheadDays = 30; // Process appointments up to 30 days in advance
            const lookAheadDate = new Date(now.getTime() + (lookAheadDays * 24 * 60 * 60 * 1000));

            const recurringAppointments = await RecurringAppointment.find({
                status: 'active',
                nextScheduledDate: { $lte: lookAheadDate }
            });

            for (const recurring of recurringAppointments) {
                await this.processRecurringAppointment(recurring);
            }

            console.log(`🔄 Processed ${recurringAppointments.length} recurring appointments`);
        } catch (error) {
            console.error('Error processing recurring appointments:', error);
        }
    }

    /**
     * Process individual recurring appointment
     * @param {Object} recurring - Recurring appointment object
     */
    async processRecurringAppointment(recurring) {
        try {
            const now = new Date();
            
            // Check if we need to create the next appointment
            if (recurring.nextScheduledDate && recurring.nextScheduledDate <= now) {
                await this.createNextAppointment(recurring);
                
                // Calculate next occurrence
                const nextDate = this.calculateNextDate(
                    recurring.nextScheduledDate,
                    recurring.recurrencePattern
                );

                // Check if we should continue or complete the series
                let shouldContinue = true;
                
                if (recurring.endDate && nextDate > recurring.endDate) {
                    shouldContinue = false;
                }
                
                if (recurring.maxOccurrences && 
                    recurring.createdAppointments.length >= recurring.maxOccurrences) {
                    shouldContinue = false;
                }

                // Update recurring appointment
                const updateData = {
                    lastProcessedDate: now,
                    nextScheduledDate: shouldContinue ? nextDate : null,
                    status: shouldContinue ? 'active' : 'completed'
                };

                await RecurringAppointment.findByIdAndUpdate(recurring._id, updateData);

                if (!shouldContinue) {
                    console.log(`✅ Completed recurring appointment series: ${recurring.recurringId}`);
                }
            }
        } catch (error) {
            console.error(`Error processing recurring appointment ${recurring.recurringId}:`, error);
        }
    }

    /**
     * Create next appointment in the series
     * @param {Object} recurring - Recurring appointment object
     */
    async createNextAppointment(recurring) {
        try {
            const scheduledDate = recurring.nextScheduledDate;
            
            // Find best available time slot
            const timeSlot = await this.findBestTimeSlot(recurring, scheduledDate);
            
            if (!timeSlot) {
                console.warn(`No available time slot for recurring appointment ${recurring.recurringId} on ${scheduledDate}`);
                return null;
            }

            // Create appointment
            const appointmentData = {
                clientId: recurring.clientId,
                petId: recurring.petId,
                clinicId: recurring.clinicId,
                appointmentDate: timeSlot,
                estimatedDuration: recurring.appointmentTemplate.estimatedDuration,
                priority: recurring.appointmentTemplate.priority,
                reason: recurring.appointmentTemplate.reason || `Recurring: ${recurring.templateName}`,
                categories: recurring.appointmentTemplate.categories || [],
                staffAssigned: recurring.appointmentTemplate.staffPreference,
                isRecurring: true,
                recurringId: recurring.recurringId,
                status: 'scheduled'
            };

            const appointment = new Appointment(appointmentData);
            await appointment.save();

            // Update recurring appointment with created appointment info
            await RecurringAppointment.findByIdAndUpdate(recurring._id, {
                $push: {
                    createdAppointments: {
                        appointmentId: appointment.appointmentId,
                        scheduledDate: timeSlot,
                        status: 'scheduled'
                    }
                }
            });

            // Send notification if advance notice is configured
            if (recurring.notificationSettings?.advanceNotice > 0) {
                await this.scheduleAdvanceNotification(recurring, appointment);
            }

            console.log(`📅 Created recurring appointment: ${appointment.appointmentId} for ${scheduledDate}`);
            return appointment;
        } catch (error) {
            console.error('Error creating next appointment:', error);
            return null;
        }
    }

    /**
     * Find best available time slot for recurring appointment
     * @param {Object} recurring - Recurring appointment object
     * @param {Date} targetDate - Target date for appointment
     */
    async findBestTimeSlot(recurring, targetDate) {
        try {
            const preferredSlots = recurring.appointmentTemplate.preferredTimeSlots || [
                { startTime: "09:00", endTime: "17:00" }
            ];

            // Generate possible time slots
            const timeSlots = this.generateTimeSlots(preferredSlots, recurring.appointmentTemplate.estimatedDuration);
            
            for (const slot of timeSlots) {
                const slotDateTime = new Date(targetDate);
                const [hours, minutes] = slot.startTime.split(':');
                slotDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

                // Skip past times
                if (slotDateTime <= new Date()) continue;

                const availability = await appointmentScheduler.checkAvailability({
                    clinicId: recurring.clinicId,
                    staffId: recurring.appointmentTemplate.staffPreference,
                    appointmentDate: slotDateTime,
                    estimatedDuration: recurring.appointmentTemplate.estimatedDuration
                });

                if (availability.available) {
                    return slotDateTime;
                }
            }

            return null;
        } catch (error) {
            console.error('Error finding time slot:', error);
            return null;
        }
    }

    /**
     * Calculate next occurrence date
     * @param {Date} currentDate - Current date
     * @param {Object} pattern - Recurrence pattern
     */
    calculateNextDate(currentDate, pattern) {
        const nextDate = new Date(currentDate);

        switch (pattern.type) {
            case 'daily':
                nextDate.setDate(nextDate.getDate() + pattern.interval);
                break;

            case 'weekly':
                if (pattern.daysOfWeek && pattern.daysOfWeek.length > 0) {
                    // Find next occurrence on specified days
                    const currentDay = nextDate.getDay();
                    const sortedDays = pattern.daysOfWeek.sort((a, b) => a - b);
                    
                    let nextDay = sortedDays.find(day => day > currentDay);
                    if (!nextDay) {
                        nextDay = sortedDays[0];
                        nextDate.setDate(nextDate.getDate() + (7 * pattern.interval));
                    }
                    
                    const daysToAdd = nextDay - currentDay;
                    nextDate.setDate(nextDate.getDate() + daysToAdd);
                } else {
                    nextDate.setDate(nextDate.getDate() + (7 * pattern.interval));
                }
                break;

            case 'monthly':
                if (pattern.dayOfMonth) {
                    nextDate.setMonth(nextDate.getMonth() + pattern.interval);
                    nextDate.setDate(pattern.dayOfMonth);
                } else {
                    nextDate.setMonth(nextDate.getMonth() + pattern.interval);
                }
                break;

            case 'yearly':
                if (pattern.monthOfYear && pattern.dayOfMonth) {
                    nextDate.setFullYear(nextDate.getFullYear() + pattern.interval);
                    nextDate.setMonth(pattern.monthOfYear - 1);
                    nextDate.setDate(pattern.dayOfMonth);
                } else {
                    nextDate.setFullYear(nextDate.getFullYear() + pattern.interval);
                }
                break;
        }

        return nextDate;
    }

    /**
     * Generate time slots from preferences
     * @param {Array} timeSlots - Preferred time slots
     * @param {number} duration - Appointment duration
     */
    generateTimeSlots(timeSlots, duration) {
        const slots = [];
        
        for (const slot of timeSlots) {
            const [startHour, startMin] = slot.startTime.split(':').map(Number);
            const [endHour, endMin] = slot.endTime.split(':').map(Number);
            
            const startMinutes = startHour * 60 + startMin;
            const endMinutes = endHour * 60 + endMin;
            
            for (let minutes = startMinutes; minutes <= endMinutes - duration; minutes += 30) {
                const hours = Math.floor(minutes / 60);
                const mins = minutes % 60;
                
                slots.push({
                    startTime: `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
                });
            }
        }
        
        return slots;
    }

    /**
     * Schedule advance notification for recurring appointment
     * @param {Object} recurring - Recurring appointment object
     * @param {Object} appointment - Created appointment
     */
    async scheduleAdvanceNotification(recurring, appointment) {
        try {
            const notificationDate = new Date(appointment.appointmentDate);
            notificationDate.setDate(notificationDate.getDate() - recurring.notificationSettings.advanceNotice);

            if (notificationDate > new Date()) {
                // Schedule notification
                const client = await this.getClientData(recurring.clientId);
                const pet = await this.getPetData(recurring.petId);

                if (client) {
                    const notification = {
                        recipient: client,
                        message: {
                            subject: 'Upcoming Recurring Appointment',
                            title: `Recurring appointment scheduled in ${recurring.notificationSettings.advanceNotice} days`,
                            body: `Dear ${client.firstName},\n\nThis is a reminder that ${pet?.petName || 'your pet'} has a recurring appointment scheduled for ${appointment.appointmentDate.toLocaleDateString()} at ${appointment.appointmentDate.toLocaleTimeString()}.\n\nAppointment: ${recurring.templateName}\n\nPlease contact us if you need to reschedule.\n\nThank you!`,
                            data: {
                                type: 'recurring_reminder',
                                appointmentId: appointment.appointmentId,
                                recurringId: recurring.recurringId
                            }
                        },
                        channels: recurring.notificationSettings.reminderChannels || ['email']
                    };

                    notificationService.scheduleNotification(notification, notificationDate);
                }
            }
        } catch (error) {
            console.error('Error scheduling advance notification:', error);
        }
    }

    /**
     * Start periodic processing
     */
    startPeriodicProcessing() {
        // Process immediately on startup
        setTimeout(() => this.processRecurringAppointments(), 5000);

        // Then process daily
        setInterval(() => {
            this.processRecurringAppointments();
        }, this.processingInterval);

        console.log('🔄 Started recurring appointments processing');
    }

    /**
     * Helper method to get client data
     */
    async getClientData(clientId) {
        try {
            const Client = mongoose.model('Client');
            return await Client.findOne({ clientId }).select('firstName lastName email phoneNumber').lean();
        } catch (error) {
            console.error('Error getting client data:', error);
            return null;
        }
    }

    /**
     * Helper method to get pet data
     */
    async getPetData(petId) {
        try {
            const Pet = mongoose.model('Pet');
            return await Pet.findOne({ petId }).select('petName species breed').lean();
        } catch (error) {
            console.error('Error getting pet data:', error);
            return null;
        }
    }
}

// Create singleton instance
const recurringAppointmentsService = new RecurringAppointmentsService();

export default recurringAppointmentsService;
export { RecurringAppointment };
