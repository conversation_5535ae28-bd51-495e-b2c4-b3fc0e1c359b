/**
 * Notification Service
 * 
 * Handles multi-channel notifications including email, SMS, and in-app notifications
 * for appointment reminders, confirmations, and updates.
 */

import nodemailer from 'nodemailer';
import twilio from 'twilio';
import { NODE_ENV, EMAIL_CONFIG, SMS_CONFIG } from '../config/env.js';
import webSocketService from '../utils/websocketService.js';
import { logAuditEvent } from '../utils/auditLogger.js';

class NotificationService {
    constructor() {
        this.emailTransporter = null;
        this.smsClient = null;
        this.initializeProviders();
    }

    /**
     * Initialize email and SMS providers
     */
    async initializeProviders() {
        try {
            // Initialize email transporter
            if (EMAIL_CONFIG?.host) {
                this.emailTransporter = nodemailer.createTransport({
                    host: EMAIL_CONFIG.host,
                    port: EMAIL_CONFIG.port || 587,
                    secure: EMAIL_CONFIG.secure || false,
                    auth: {
                        user: EMAIL_CONFIG.user,
                        pass: EMAIL_CONFIG.password
                    }
                });

                // Verify email connection
                await this.emailTransporter.verify();
                console.log('✅ Email service initialized');
            }

            // Initialize SMS client
            if (SMS_CONFIG?.accountSid && SMS_CONFIG?.authToken) {
                this.smsClient = twilio(SMS_CONFIG.accountSid, SMS_CONFIG.authToken);
                console.log('✅ SMS service initialized');
            }
        } catch (error) {
            console.error('❌ Error initializing notification providers:', error);
        }
    }

    /**
     * Send appointment confirmation
     * @param {Object} appointment - Appointment data
     * @param {Object} client - Client data
     * @param {Array} channels - Notification channels ['email', 'sms', 'app']
     */
    async sendAppointmentConfirmation(appointment, client, channels = ['email']) {
        try {
            const message = {
                subject: 'Appointment Confirmation',
                title: 'Your appointment has been confirmed',
                body: `Dear ${client.firstName},\n\nYour appointment for ${appointment.petName} has been confirmed.\n\nDetails:\nDate: ${new Date(appointment.appointmentDate).toLocaleDateString()}\nTime: ${new Date(appointment.appointmentDate).toLocaleTimeString()}\nClinic: ${appointment.clinicName || 'VetCare Clinic'}\n\nPlease arrive 15 minutes early.\n\nThank you!`,
                data: {
                    appointmentId: appointment.appointmentId,
                    type: 'confirmation',
                    appointment,
                    client
                }
            };

            const results = await this.sendMultiChannel(client, message, channels);
            
            // Log notification
            await logAuditEvent({
                action: 'NOTIFICATION_SENT',
                entityType: 'Appointment',
                entityId: appointment.appointmentId.toString(),
                performedBy: {
                    userType: 'system'
                },
                clinicId: appointment.clinicId,
                metadata: {
                    category: 'BUSINESS',
                    severity: 'LOW'
                },
                requestDetails: {
                    channels,
                    recipient: client.email || client.phoneNumber
                }
            });

            return results;
        } catch (error) {
            console.error('Error sending appointment confirmation:', error);
            throw error;
        }
    }

    /**
     * Send appointment reminder
     * @param {Object} appointment - Appointment data
     * @param {Object} client - Client data
     * @param {string} reminderType - '24h', '2h', '30m'
     * @param {Array} channels - Notification channels
     */
    async sendAppointmentReminder(appointment, client, reminderType = '24h', channels = ['email', 'sms']) {
        try {
            const timeMap = {
                '24h': '24 hours',
                '2h': '2 hours',
                '30m': '30 minutes'
            };

            const message = {
                subject: `Appointment Reminder - ${timeMap[reminderType]}`,
                title: `Upcoming appointment in ${timeMap[reminderType]}`,
                body: `Dear ${client.firstName},\n\nThis is a reminder that ${appointment.petName} has an appointment in ${timeMap[reminderType]}.\n\nDetails:\nDate: ${new Date(appointment.appointmentDate).toLocaleDateString()}\nTime: ${new Date(appointment.appointmentDate).toLocaleTimeString()}\nClinic: ${appointment.clinicName || 'VetCare Clinic'}\n\nPlease arrive 15 minutes early. If you need to reschedule, please call us as soon as possible.\n\nThank you!`,
                data: {
                    appointmentId: appointment.appointmentId,
                    type: 'reminder',
                    reminderType,
                    appointment,
                    client
                }
            };

            const results = await this.sendMultiChannel(client, message, channels);
            
            // Log notification
            await logAuditEvent({
                action: 'NOTIFICATION_SENT',
                entityType: 'Appointment',
                entityId: appointment.appointmentId.toString(),
                performedBy: {
                    userType: 'system'
                },
                clinicId: appointment.clinicId,
                metadata: {
                    category: 'BUSINESS',
                    severity: 'MEDIUM'
                },
                requestDetails: {
                    reminderType,
                    channels,
                    recipient: client.email || client.phoneNumber
                }
            });

            return results;
        } catch (error) {
            console.error('Error sending appointment reminder:', error);
            throw error;
        }
    }

    /**
     * Send appointment update notification
     * @param {Object} appointment - Updated appointment data
     * @param {Object} client - Client data
     * @param {string} updateType - 'rescheduled', 'cancelled', 'completed'
     * @param {Array} channels - Notification channels
     */
    async sendAppointmentUpdate(appointment, client, updateType, channels = ['email', 'app']) {
        try {
            const updateMessages = {
                rescheduled: {
                    subject: 'Appointment Rescheduled',
                    title: 'Your appointment has been rescheduled',
                    body: `Dear ${client.firstName},\n\nYour appointment for ${appointment.petName} has been rescheduled.\n\nNew Details:\nDate: ${new Date(appointment.appointmentDate).toLocaleDateString()}\nTime: ${new Date(appointment.appointmentDate).toLocaleTimeString()}\n\nWe apologize for any inconvenience.\n\nThank you!`
                },
                cancelled: {
                    subject: 'Appointment Cancelled',
                    title: 'Your appointment has been cancelled',
                    body: `Dear ${client.firstName},\n\nYour appointment for ${appointment.petName} scheduled for ${new Date(appointment.appointmentDate).toLocaleDateString()} has been cancelled.\n\nPlease contact us to reschedule.\n\nThank you!`
                },
                completed: {
                    subject: 'Appointment Completed',
                    title: 'Your appointment is complete',
                    body: `Dear ${client.firstName},\n\nThank you for visiting us today with ${appointment.petName}. Your appointment has been completed.\n\nYou can view the medical records and any follow-up instructions in your client portal.\n\nThank you for choosing our clinic!`
                }
            };

            const message = {
                ...updateMessages[updateType],
                data: {
                    appointmentId: appointment.appointmentId,
                    type: 'update',
                    updateType,
                    appointment,
                    client
                }
            };

            const results = await this.sendMultiChannel(client, message, channels);
            
            // Log notification
            await logAuditEvent({
                action: 'NOTIFICATION_SENT',
                entityType: 'Appointment',
                entityId: appointment.appointmentId.toString(),
                performedBy: {
                    userType: 'system'
                },
                clinicId: appointment.clinicId,
                metadata: {
                    category: 'BUSINESS',
                    severity: updateType === 'cancelled' ? 'HIGH' : 'MEDIUM'
                },
                requestDetails: {
                    updateType,
                    channels,
                    recipient: client.email || client.phoneNumber
                }
            });

            return results;
        } catch (error) {
            console.error('Error sending appointment update:', error);
            throw error;
        }
    }

    /**
     * Send multi-channel notification
     * @param {Object} recipient - Recipient data
     * @param {Object} message - Message content
     * @param {Array} channels - Channels to send through
     */
    async sendMultiChannel(recipient, message, channels) {
        const results = {
            email: null,
            sms: null,
            app: null,
            success: false,
            errors: []
        };

        // Send email
        if (channels.includes('email') && recipient.email) {
            try {
                results.email = await this.sendEmail(recipient.email, message.subject, message.body);
            } catch (error) {
                results.errors.push({ channel: 'email', error: error.message });
            }
        }

        // Send SMS
        if (channels.includes('sms') && recipient.phoneNumber) {
            try {
                results.sms = await this.sendSMS(recipient.phoneNumber, message.body);
            } catch (error) {
                results.errors.push({ channel: 'sms', error: error.message });
            }
        }

        // Send in-app notification
        if (channels.includes('app')) {
            try {
                results.app = await this.sendInAppNotification(recipient, message);
            } catch (error) {
                results.errors.push({ channel: 'app', error: error.message });
            }
        }

        results.success = results.errors.length === 0;
        return results;
    }

    /**
     * Send email notification
     * @param {string} to - Recipient email
     * @param {string} subject - Email subject
     * @param {string} text - Email body
     */
    async sendEmail(to, subject, text) {
        if (!this.emailTransporter) {
            throw new Error('Email service not configured');
        }

        const mailOptions = {
            from: EMAIL_CONFIG.from || '<EMAIL>',
            to,
            subject,
            text,
            html: this.generateEmailHTML(subject, text)
        };

        const result = await this.emailTransporter.sendMail(mailOptions);
        console.log(`📧 Email sent to ${to}: ${result.messageId}`);
        return result;
    }

    /**
     * Send SMS notification
     * @param {string} to - Recipient phone number
     * @param {string} body - SMS body
     */
    async sendSMS(to, body) {
        if (!this.smsClient) {
            throw new Error('SMS service not configured');
        }

        // Truncate SMS to 160 characters
        const truncatedBody = body.length > 160 ? body.substring(0, 157) + '...' : body;

        const result = await this.smsClient.messages.create({
            body: truncatedBody,
            from: SMS_CONFIG.phoneNumber,
            to
        });

        console.log(`📱 SMS sent to ${to}: ${result.sid}`);
        return result;
    }

    /**
     * Send in-app notification via WebSocket
     * @param {Object} recipient - Recipient data
     * @param {Object} message - Message content
     */
    async sendInAppNotification(recipient, message) {
        const notification = {
            id: `notif_${Date.now()}`,
            title: message.title,
            body: message.body,
            type: message.data.type,
            data: message.data,
            timestamp: new Date(),
            read: false
        };

        // Send via WebSocket if user is connected
        const sent = webSocketService.sendToUser(recipient.userId || recipient.clientId, 'notification', notification);
        
        console.log(`🔔 In-app notification sent to user ${recipient.userId || recipient.clientId}: ${sent ? 'delivered' : 'queued'}`);
        
        return { delivered: sent, notification };
    }

    /**
     * Generate HTML email template
     * @param {string} subject - Email subject
     * @param {string} text - Email text
     */
    generateEmailHTML(subject, text) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${subject}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>VetCare</h1>
                </div>
                <div class="content">
                    <h2>${subject}</h2>
                    <p>${text.replace(/\n/g, '<br>')}</p>
                </div>
                <div class="footer">
                    <p>This is an automated message from VetCare. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    /**
     * Schedule notification for future delivery
     * @param {Object} notification - Notification data
     * @param {Date} scheduledTime - When to send
     */
    scheduleNotification(notification, scheduledTime) {
        const delay = scheduledTime.getTime() - Date.now();
        
        if (delay > 0) {
            setTimeout(async () => {
                try {
                    await this.sendMultiChannel(
                        notification.recipient,
                        notification.message,
                        notification.channels
                    );
                } catch (error) {
                    console.error('Error sending scheduled notification:', error);
                }
            }, delay);
            
            console.log(`📅 Notification scheduled for ${scheduledTime.toISOString()}`);
            return true;
        }
        
        return false;
    }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
