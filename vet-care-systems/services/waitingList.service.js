/**
 * Waiting List Service
 * 
 * Manages appointment waiting lists for fully booked slots with automated notifications
 * when slots become available.
 */

import mongoose from 'mongoose';
import { getNextSequence } from '../utils/counterManager.js';
import notificationService from './notification.service.js';
import appointmentScheduler from './appointmentScheduler.service.js';
import webSocketService from '../utils/websocketService.js';

// Waiting List Schema
const waitingListSchema = new mongoose.Schema({
    waitingListId: {
        type: Number,
        unique: true
    },
    clientId: {
        type: Number,
        required: true,
        ref: 'Client'
    },
    petId: {
        type: Number,
        required: true,
        ref: 'Pet'
    },
    clinicId: {
        type: Number,
        required: true,
        ref: 'Clinic'
    },
    preferredDate: {
        type: Date,
        required: true
    },
    preferredTimeSlots: [{
        startTime: String, // "09:00"
        endTime: String    // "17:00"
    }],
    estimatedDuration: {
        type: Number,
        default: 30
    },
    priority: {
        type: String,
        enum: ['low', 'normal', 'high', 'emergency'],
        default: 'normal'
    },
    categories: [{
        appointmentCategoryId: Number,
        notes: String
    }],
    staffPreference: {
        type: Number,
        ref: 'Staff'
    },
    reason: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['active', 'notified', 'expired', 'fulfilled', 'cancelled'],
        default: 'active'
    },
    notificationPreferences: {
        email: { type: Boolean, default: true },
        sms: { type: Boolean, default: true },
        app: { type: Boolean, default: true }
    },
    expiresAt: {
        type: Date,
        default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    },
    notifiedAt: Date,
    fulfilledAt: Date,
    appointmentId: Number // Set when appointment is created
}, {
    timestamps: true
});

// Auto-increment waitingListId
waitingListSchema.pre('save', async function(next) {
    if (this.isNew) {
        this.waitingListId = await getNextSequence('waitingList');
    }
    next();
});

// Index for efficient querying
waitingListSchema.index({ clinicId: 1, preferredDate: 1, status: 1 });
waitingListSchema.index({ clientId: 1, status: 1 });
waitingListSchema.index({ expiresAt: 1 });

const WaitingList = mongoose.model('WaitingList', waitingListSchema);

class WaitingListService {
    constructor() {
        this.checkInterval = 5 * 60 * 1000; // Check every 5 minutes
        this.startPeriodicCheck();
    }

    /**
     * Add client to waiting list
     * @param {Object} waitingListData - Waiting list entry data
     * @returns {Promise<Object>} Created waiting list entry
     */
    async addToWaitingList(waitingListData) {
        try {
            const waitingListEntry = new WaitingList(waitingListData);
            await waitingListEntry.save();

            // Notify client they've been added to waiting list
            const client = await this.getClientData(waitingListData.clientId);
            const pet = await this.getPetData(waitingListData.petId);

            if (client) {
                const channels = [];
                if (waitingListData.notificationPreferences?.email) channels.push('email');
                if (waitingListData.notificationPreferences?.sms) channels.push('sms');
                if (waitingListData.notificationPreferences?.app) channels.push('app');

                await notificationService.sendMultiChannel(client, {
                    subject: 'Added to Waiting List',
                    title: 'You\'ve been added to our waiting list',
                    body: `Dear ${client.firstName},\n\nYou've been successfully added to our waiting list for ${pet?.petName || 'your pet'} on ${new Date(waitingListData.preferredDate).toLocaleDateString()}.\n\nWe'll notify you as soon as a slot becomes available.\n\nThank you for your patience!`,
                    data: {
                        type: 'waiting_list_added',
                        waitingListId: waitingListEntry.waitingListId,
                        preferredDate: waitingListData.preferredDate
                    }
                }, channels);
            }

            // Broadcast to clinic staff
            webSocketService.broadcastToClinic(waitingListData.clinicId, 'waiting_list_update', {
                type: 'new_entry',
                waitingListEntry,
                timestamp: new Date()
            });

            console.log(`✅ Added to waiting list: ${waitingListEntry.waitingListId}`);
            return waitingListEntry;
        } catch (error) {
            console.error('Error adding to waiting list:', error);
            throw error;
        }
    }

    /**
     * Check for available slots and notify waiting list
     * @param {Object} params - Check parameters
     */
    async checkAvailableSlots({ clinicId, date, staffId = null }) {
        try {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            // Get active waiting list entries for this date
            const waitingEntries = await WaitingList.find({
                clinicId,
                preferredDate: {
                    $gte: startOfDay,
                    $lte: endOfDay
                },
                status: 'active',
                expiresAt: { $gt: new Date() }
            }).sort({ priority: -1, createdAt: 1 }); // Priority first, then FIFO

            for (const entry of waitingEntries) {
                // Check if slots are available for this entry
                const slotsToCheck = this.generateTimeSlots(entry.preferredTimeSlots, entry.estimatedDuration);
                
                for (const slot of slotsToCheck) {
                    const slotDateTime = new Date(entry.preferredDate);
                    const [hours, minutes] = slot.startTime.split(':');
                    slotDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

                    const availability = await appointmentScheduler.checkAvailability({
                        clinicId: entry.clinicId,
                        staffId: entry.staffPreference || staffId,
                        appointmentDate: slotDateTime,
                        estimatedDuration: entry.estimatedDuration
                    });

                    if (availability.available) {
                        await this.notifyAvailableSlot(entry, slotDateTime);
                        break; // Move to next waiting list entry
                    }
                }
            }
        } catch (error) {
            console.error('Error checking available slots:', error);
        }
    }

    /**
     * Notify client about available slot
     * @param {Object} waitingEntry - Waiting list entry
     * @param {Date} availableSlot - Available time slot
     */
    async notifyAvailableSlot(waitingEntry, availableSlot) {
        try {
            const client = await this.getClientData(waitingEntry.clientId);
            const pet = await this.getPetData(waitingEntry.petId);

            if (!client) return;

            // Update waiting list status
            await WaitingList.findByIdAndUpdate(waitingEntry._id, {
                status: 'notified',
                notifiedAt: new Date()
            });

            // Prepare notification channels
            const channels = [];
            if (waitingEntry.notificationPreferences?.email) channels.push('email');
            if (waitingEntry.notificationPreferences?.sms) channels.push('sms');
            if (waitingEntry.notificationPreferences?.app) channels.push('app');

            // Send notification
            await notificationService.sendMultiChannel(client, {
                subject: 'Appointment Slot Available!',
                title: 'A slot is now available for your appointment',
                body: `Dear ${client.firstName},\n\nGreat news! A slot is now available for ${pet?.petName || 'your pet'} on ${availableSlot.toLocaleDateString()} at ${availableSlot.toLocaleTimeString()}.\n\nPlease contact us within 2 hours to confirm this appointment, or it will be offered to the next person on the waiting list.\n\nThank you!`,
                data: {
                    type: 'slot_available',
                    waitingListId: waitingEntry.waitingListId,
                    availableSlot,
                    expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours to respond
                }
            }, channels);

            // Broadcast to clinic staff
            webSocketService.broadcastToClinic(waitingEntry.clinicId, 'waiting_list_notification', {
                type: 'slot_offered',
                waitingListId: waitingEntry.waitingListId,
                clientName: `${client.firstName} ${client.lastName}`,
                petName: pet?.petName,
                availableSlot,
                timestamp: new Date()
            });

            console.log(`📞 Notified waiting list entry ${waitingEntry.waitingListId} about available slot`);
        } catch (error) {
            console.error('Error notifying about available slot:', error);
        }
    }

    /**
     * Confirm appointment from waiting list
     * @param {number} waitingListId - Waiting list ID
     * @param {Object} appointmentData - Appointment data
     */
    async confirmAppointment(waitingListId, appointmentData) {
        try {
            const waitingEntry = await WaitingList.findOne({ 
                waitingListId, 
                status: 'notified' 
            });

            if (!waitingEntry) {
                throw new Error('Waiting list entry not found or not in notified status');
            }

            // Update waiting list status
            await WaitingList.findByIdAndUpdate(waitingEntry._id, {
                status: 'fulfilled',
                fulfilledAt: new Date(),
                appointmentId: appointmentData.appointmentId
            });

            // Broadcast to clinic staff
            webSocketService.broadcastToClinic(waitingEntry.clinicId, 'waiting_list_update', {
                type: 'appointment_confirmed',
                waitingListId,
                appointmentId: appointmentData.appointmentId,
                timestamp: new Date()
            });

            console.log(`✅ Confirmed appointment from waiting list: ${waitingListId}`);
            return true;
        } catch (error) {
            console.error('Error confirming appointment from waiting list:', error);
            throw error;
        }
    }

    /**
     * Cancel waiting list entry
     * @param {number} waitingListId - Waiting list ID
     * @param {string} reason - Cancellation reason
     */
    async cancelWaitingListEntry(waitingListId, reason = 'Client cancelled') {
        try {
            const waitingEntry = await WaitingList.findOneAndUpdate(
                { waitingListId, status: { $in: ['active', 'notified'] } },
                { 
                    status: 'cancelled',
                    cancelledAt: new Date(),
                    cancellationReason: reason
                },
                { new: true }
            );

            if (!waitingEntry) {
                throw new Error('Waiting list entry not found or already processed');
            }

            // Broadcast to clinic staff
            webSocketService.broadcastToClinic(waitingEntry.clinicId, 'waiting_list_update', {
                type: 'entry_cancelled',
                waitingListId,
                reason,
                timestamp: new Date()
            });

            console.log(`❌ Cancelled waiting list entry: ${waitingListId}`);
            return waitingEntry;
        } catch (error) {
            console.error('Error cancelling waiting list entry:', error);
            throw error;
        }
    }

    /**
     * Get waiting list for clinic
     * @param {number} clinicId - Clinic ID
     * @param {Object} filters - Additional filters
     */
    async getWaitingList(clinicId, filters = {}) {
        try {
            const query = { clinicId, ...filters };
            
            const waitingList = await WaitingList.find(query)
                .sort({ priority: -1, createdAt: 1 })
                .lean();

            // Enrich with client and pet data
            for (const entry of waitingList) {
                entry.client = await this.getClientData(entry.clientId);
                entry.pet = await this.getPetData(entry.petId);
            }

            return waitingList;
        } catch (error) {
            console.error('Error getting waiting list:', error);
            throw error;
        }
    }

    /**
     * Generate time slots from preferences
     * @param {Array} timeSlots - Preferred time slots
     * @param {number} duration - Appointment duration
     */
    generateTimeSlots(timeSlots, duration) {
        const slots = [];
        
        for (const slot of timeSlots) {
            const [startHour, startMin] = slot.startTime.split(':').map(Number);
            const [endHour, endMin] = slot.endTime.split(':').map(Number);
            
            const startMinutes = startHour * 60 + startMin;
            const endMinutes = endHour * 60 + endMin;
            
            for (let minutes = startMinutes; minutes <= endMinutes - duration; minutes += 30) {
                const hours = Math.floor(minutes / 60);
                const mins = minutes % 60;
                
                slots.push({
                    startTime: `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
                });
            }
        }
        
        return slots;
    }

    /**
     * Start periodic check for available slots
     */
    startPeriodicCheck() {
        setInterval(async () => {
            try {
                // Get all clinics with active waiting lists
                const clinicsWithWaiting = await WaitingList.distinct('clinicId', {
                    status: 'active',
                    expiresAt: { $gt: new Date() }
                });

                for (const clinicId of clinicsWithWaiting) {
                    // Check for today and tomorrow
                    const today = new Date();
                    const tomorrow = new Date(today);
                    tomorrow.setDate(today.getDate() + 1);

                    await this.checkAvailableSlots({ clinicId, date: today });
                    await this.checkAvailableSlots({ clinicId, date: tomorrow });
                }
            } catch (error) {
                console.error('Error in periodic waiting list check:', error);
            }
        }, this.checkInterval);

        console.log('🔄 Started periodic waiting list check');
    }

    /**
     * Helper method to get client data
     */
    async getClientData(clientId) {
        try {
            const Client = mongoose.model('Client');
            return await Client.findOne({ clientId }).select('firstName lastName email phoneNumber').lean();
        } catch (error) {
            console.error('Error getting client data:', error);
            return null;
        }
    }

    /**
     * Helper method to get pet data
     */
    async getPetData(petId) {
        try {
            const Pet = mongoose.model('Pet');
            return await Pet.findOne({ petId }).select('petName species breed').lean();
        } catch (error) {
            console.error('Error getting pet data:', error);
            return null;
        }
    }
}

// Create singleton instance
const waitingListService = new WaitingListService();

export default waitingListService;
export { WaitingList };
