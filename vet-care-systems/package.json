{"name": "vet-care-systems", "version": "0.0.0", "private": true, "type": "module", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "NODE_ENV=test node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "NODE_ENV=test node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:coverage": "NODE_ENV=test node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage"}, "dependencies": {"@arcjet/node": "^1.0.0-beta.2", "@upstash/workflow": "^0.2.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "csurf": "^1.10.0", "debug": "~2.6.9", "dotenv": "^16.4.7", "express": "5.0.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.13.0", "mongoose": "^8.10.0", "mongoose-sequence": "^6.0.1", "morgan": "~1.9.1", "nodemailer": "^7.0.3", "socket.io": "^4.8.1", "twilio": "^5.7.1", "winston": "^3.17.0", "zod": "^3.25.63"}, "devDependencies": {"@eslint/js": "^9.20.0", "eslint": "^9.20.0", "globals": "^15.14.0", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.9", "supertest": "^7.1.0"}}