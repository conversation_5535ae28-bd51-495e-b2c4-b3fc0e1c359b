import Appointment from '../models/appointment.model.js';
import AppointmentCategory from '../models/appointmentCategory.model.js';
import CategoryService from '../models/categoryService.model.js';
import Pet from '../models/pet.model.js';
import Client from '../models/client.model.js';
import Staff from '../models/staff.model.js';
import Clinic from '../models/clinic.model.js';
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import appointmentScheduler from '../services/appointmentScheduler.service.js';
import notificationService from '../services/notification.service.js';
import waitingListService from '../services/waitingList.service.js';
// import recurringAppointmentsService from '../services/recurringAppointments.service.js'; // Temporarily disabled
import webSocketService from '../utils/websocketService.js';
import mongoose from 'mongoose';
import { openai } from '../config/ai.js';
import { detectEmergency } from '../config/ai.js';

// Helper function to validate and parse appointment ID
const validateAppointmentId = (appointmentId) => {
    if (!appointmentId || appointmentId === 'undefined' || appointmentId === 'null') {
        throw new Error("Appointment ID is required");
    }

    const parsedId = parseInt(appointmentId);
    if (isNaN(parsedId) || parsedId <= 0) {
        throw new Error(`Invalid appointment ID format: "${appointmentId}". Expected a positive number.`);
    }

    return parsedId;
};

// Helper function to get current user info
const getCurrentUserInfo = (req) => {
    const staffId = req.user?.staffId || req.user?.userId;
    const staffName = req.user?.firstName && req.user?.lastName
        ? `${req.user.firstName} ${req.user.lastName}`.trim()
        : 'Staff Member';
    const jobTitle = req.user?.jobTitle || 'Staff';

    return { staffId, staffName, jobTitle };
};

/**
 * Unified appointment booking for registered clients and walk-ins
 */
export const unifiedAppointmentBooking = async (req, res) => {
    try {
        const { clinicId } = req.user;
        const {
            clientType, // 'registered' or 'walk-in'
            clientData,
            petData,
            appointmentData
        } = req.body;

        if (!clinicId) {
            return sendResponse(res, 400, false, "Clinic context is required");
        }

        if (!clientType || !['registered', 'walk-in'].includes(clientType)) {
            return sendResponse(res, 400, false, "Valid client type is required (registered or walk-in)");
        }

        let client, pet;

        // Handle client registration/retrieval
        if (clientType === 'walk-in') {
            // Quick walk-in client registration
            client = await Client.create({
                firstName: clientData.firstName,
                lastName: clientData.lastName,
                phoneNumber: clientData.phoneNumber,
                email: clientData.email || `${clientData.phoneNumber}@walkin.temp`,
                isWalkIn: true,
                registrationSource: 'walk-in',
                preferredClinicId: clinicId,
                status: 1
            });

            // Create pet for walk-in client
            pet = await Pet.create({
                petName: petData.petName,
                species: petData.species,
                breed: petData.breed,
                gender: petData.gender || 'unknown',
                age: petData.age || 0,
                weight: petData.weight || 0,
                clientId: client.clientId,
                clinicId: clinicId,
                status: 1
            });
        } else {
            // Registered client - verify existence
            if (!clientData.clientId) {
                return sendResponse(res, 400, false, "Client ID is required for registered clients");
            }

            client = await Client.findOne({ clientId: clientData.clientId });
            if (!client) {
                return sendResponse(res, 404, false, "Client not found");
            }

            if (petData.petId) {
                // Existing pet
                pet = await Pet.findOne({ petId: petData.petId, clientId: client.clientId });
                if (!pet) {
                    return sendResponse(res, 404, false, "Pet not found or doesn't belong to client");
                }
            } else {
                // New pet for existing client
                pet = await Pet.create({
                    petName: petData.petName,
                    species: petData.species,
                    breed: petData.breed,
                    gender: petData.gender || 'unknown',
                    age: petData.age || 0,
                    weight: petData.weight || 0,
                    clientId: client.clientId,
                    clinicId: clinicId,
                    status: 1
                });
            }
        }

        // AI-powered emergency detection
        const symptoms = appointmentData.notes || appointmentData.reason || '';
        const isEmergency = detectEmergency(symptoms);

        if (isEmergency) {
            appointmentData.priority = 'urgent';
            console.log(`🚨 Emergency detected for appointment: ${symptoms}`);
        }

        // Get staff in charge (default to current user if not specified)
        const currentUser = getCurrentUserInfo(req);
        const staffInCharge = appointmentData.staffInCharge || currentUser.staffId;

        // Verify staff exists and has access to clinic
        const staff = await Staff.findOne({
            staffId: staffInCharge,
            $or: [
                { clinicId: clinicId },
                { additionalClinics: clinicId }
            ]
        });

        if (!staff) {
            return sendResponse(res, 404, false, "Assigned staff not found or doesn't have access to this clinic");
        }

        // Create appointment with denormalized data
        const appointmentPayload = {
            petId: pet.petId,
            petName: pet.petName,
            petSpecies: pet.species,
            petBreed: pet.breed,
            petGender: pet.gender,
            petAge: pet.age,
            petWeight: pet.weight,

            clientId: client.clientId,
            clientName: `${client.firstName} ${client.lastName}`,
            clientPhone: client.phoneNumber,
            clientEmail: client.email,
            isWalkInClient: client.isWalkIn || false,

            clinicId: clinicId,

            staffInCharge: staff.staffId,
            staffInChargeName: `${staff.firstName || ''} ${staff.lastName || ''}`.trim(),
            staffInChargeJobTitle: staff.jobTitle || 'Staff',

            appointmentDate: new Date(appointmentData.appointmentDate),
            appointmentTime: appointmentData.appointmentTime,
            estimatedDuration: appointmentData.estimatedDuration || 30,
            priority: appointmentData.priority || 'normal',
            status: 'scheduled',

            notes: appointmentData.notes || '',
            appointmentCategories: appointmentData.categories || [],

            createdBy: currentUser.staffId,
            lastModifiedBy: currentUser.staffId
        };

        // Populate clinic name
        const clinic = await Clinic.findOne({ clinicId });
        if (clinic) {
            appointmentPayload.clinicName = clinic.clinicName;
        }

        const appointment = await Appointment.create(appointmentPayload);

        // Send real-time notification
        webSocketService.notifyClinic(clinicId, 'new_appointment', {
            appointmentId: appointment.appointmentId,
            clientName: appointment.clientName,
            petName: appointment.petName,
            appointmentDate: appointment.appointmentDate,
            isWalkIn: client.isWalkIn,
            isEmergency: isEmergency
        });

        // Send notification if emergency
        if (isEmergency) {
            await notificationService.sendEmergencyAlert(clinicId, {
                appointmentId: appointment.appointmentId,
                clientName: appointment.clientName,
                petName: appointment.petName,
                symptoms: symptoms
            });
        }

        return sendResponse(res, 201, true, "Appointment booked successfully", {
            appointment: {
                appointmentId: appointment.appointmentId,
                appointmentDate: appointment.appointmentDate,
                appointmentTime: appointment.appointmentTime,
                status: appointment.status,
                priority: appointment.priority,
                isEmergency: isEmergency
            },
            client: {
                clientId: client.clientId,
                name: `${client.firstName} ${client.lastName}`,
                isWalkIn: client.isWalkIn
            },
            pet: {
                petId: pet.petId,
                petName: pet.petName,
                species: pet.species,
                breed: pet.breed
            }
        });

    } catch (error) {
        console.error("Error in unified appointment booking:", error);
        return sendResponse(res, 500, false, "Error booking appointment: " + error.message);
    }
};

/**
 * Check for appointment scheduling conflicts
 */
export const checkAppointmentConflicts = async (req, res) => {
    try {
        const { clinicId } = req.user;
        const { date, time, duration = 30, staffId } = req.query;

        if (!date || !time) {
            return sendResponse(res, 400, false, "Date and time are required");
        }

        const appointmentDateTime = new Date(`${date}T${time}`);
        const endDateTime = new Date(appointmentDateTime.getTime() + (duration * 60000));

        // Check for conflicts
        const conflicts = await Appointment.find({
            clinicId: clinicId,
            appointmentDate: {
                $gte: new Date(appointmentDateTime.getTime() - (30 * 60000)), // 30 min buffer before
                $lte: new Date(endDateTime.getTime() + (30 * 60000)) // 30 min buffer after
            },
            status: { $in: ['scheduled', 'in_progress'] }
        });

        // Check staff-specific conflicts if staffId provided
        let staffConflicts = [];
        if (staffId) {
            staffConflicts = conflicts.filter(apt => apt.staffInCharge === parseInt(staffId));
        }

        // Check clinic capacity
        const clinicCapacity = await getClinicCapacity(clinicId, appointmentDateTime);
        const currentBookings = conflicts.length;

        const hasConflicts = staffConflicts.length > 0 || currentBookings >= clinicCapacity;

        return sendResponse(res, 200, true, "Conflict check completed", {
            hasConflicts,
            conflicts: {
                staff: staffConflicts.map(apt => ({
                    appointmentId: apt.appointmentId,
                    clientName: apt.clientName,
                    petName: apt.petName,
                    appointmentDate: apt.appointmentDate,
                    staffInChargeName: apt.staffInChargeName
                })),
                clinic: {
                    currentBookings,
                    capacity: clinicCapacity,
                    isOverCapacity: currentBookings >= clinicCapacity
                }
            },
            suggestions: hasConflicts ? await getAlternativeTimeSlots(clinicId, appointmentDateTime, duration) : []
        });

    } catch (error) {
        console.error("Error checking appointment conflicts:", error);
        return sendResponse(res, 500, false, "Error checking conflicts: " + error.message);
    }
};

/**
 * Get alternative time slots when conflicts exist
 */
const getAlternativeTimeSlots = async (clinicId, requestedDateTime, duration) => {
    const alternatives = [];
    const clinic = await Clinic.findOne({ clinicId });

    if (!clinic || !clinic.operatingHours) {
        return alternatives;
    }

    const requestedDate = new Date(requestedDateTime);
    const dayName = requestedDate.toLocaleDateString('en-US', { weekday: 'lowercase' });
    const operatingHours = clinic.operatingHours[dayName];

    if (!operatingHours || !operatingHours.open || !operatingHours.close) {
        return alternatives;
    }

    // Generate time slots for the day
    const openTime = new Date(`${requestedDate.toDateString()} ${operatingHours.open}`);
    const closeTime = new Date(`${requestedDate.toDateString()} ${operatingHours.close}`);

    for (let time = new Date(openTime); time < closeTime; time.setMinutes(time.getMinutes() + 30)) {
        const endTime = new Date(time.getTime() + (duration * 60000));

        if (endTime <= closeTime) {
            // Check if this slot is available
            const conflicts = await Appointment.find({
                clinicId,
                appointmentDate: {
                    $gte: time,
                    $lt: endTime
                },
                status: { $in: ['scheduled', 'in_progress'] }
            });

            if (conflicts.length === 0) {
                alternatives.push({
                    time: time.toTimeString().slice(0, 5),
                    available: true,
                    duration: duration
                });
            }
        }

        if (alternatives.length >= 5) break; // Limit to 5 suggestions
    }

    return alternatives;
};

/**
 * Get clinic capacity for a specific time
 */
const getClinicCapacity = async (clinicId, dateTime) => {
    const clinic = await Clinic.findOne({ clinicId });

    // Default capacity based on subscription plan
    let baseCapacity = 5; // Basic plan

    if (clinic?.subscription?.plan === 'professional') {
        baseCapacity = 15;
    } else if (clinic?.subscription?.plan === 'enterprise') {
        baseCapacity = 50;
    }

    // Adjust based on available staff
    const availableStaff = await Staff.find({
        $or: [
            { clinicId: clinicId },
            { additionalClinics: clinicId }
        ],
        status: 1
    });

    return Math.min(baseCapacity, availableStaff.length * 3); // 3 appointments per staff member max
};

// Helper function to populate denormalized data for new appointments
const populateDenormalizedData = async (appointmentData) => {
    try {
        // Validate staff ID before querying
        if (!appointmentData.staffInCharge || isNaN(parseInt(appointmentData.staffInCharge))) {
            throw new Error(`Invalid staff ID: ${appointmentData.staffInCharge}`);
        }

        // Fetch all required data in parallel
        const [pet, client, staff, clinic] = await Promise.all([
            Pet.findOne({ petId: appointmentData.petId })
                .populate('speciesId', 'speciesName')
                .populate('breedId', 'breedName')
                .lean(),
            Client.findOne({ clientId: appointmentData.clientId }).lean(),
            Staff.findOne({ staffId: parseInt(appointmentData.staffInCharge) }).lean(),
            Clinic.findOne({ clinicId: appointmentData.clinicId }).lean()
        ]);

        if (!pet) throw new Error(`Pet with ID ${appointmentData.petId} not found`);
        if (!client) throw new Error(`Client with ID ${appointmentData.clientId} not found`);
        if (!staff) throw new Error(`Staff with ID ${appointmentData.staffInCharge} not found`);
        if (!clinic) throw new Error(`Clinic with ID ${appointmentData.clinicId} not found`);

        // Calculate pet age in months
        const ageInMonths = pet.dateOfBirth
            ? Math.floor((new Date() - new Date(pet.dateOfBirth)) / (1000 * 60 * 60 * 24 * 30.44))
            : 0;

        // Add denormalized data
        return {
            ...appointmentData,
            // Pet data - handle both petName and name fields for compatibility
            petName: pet.petName || pet.name || 'Unknown Pet',
            petSpecies: pet.speciesId?.speciesName || 'Dog',
            petBreed: pet.breedId?.breedName || 'Mixed Breed',
            petGender: pet.gender,
            petAge: ageInMonths,
            petWeight: pet.weight,

            // Client data
            clientName: `${client.firstName} ${client.lastName}`.trim(),
            clientPhone: client.phoneNumber,
            clientEmail: client.email,

            // Staff data
            staffInChargeName: `${staff.firstName} ${staff.lastName}`.trim(),
            staffInChargeJobTitle: staff.jobTitle,

            // Clinic data
            clinicName: clinic.clinicName
        };
    } catch (error) {
        console.error('Error populating denormalized data:', error);
        throw error;
    }
};

/**
 * Create a new appointment with service categories
 */
const createAppointment = async (req, res) => {
    try {
        const {
            petId,
            clientId,
            clinicId,
            staffInCharge,
            appointmentDate = new Date(),
            priority = 'normal',
            appointmentCategories = [], // Using the correct field name from model
            generalNotes,
            recommendations
        } = req.body;

        // Get current user info
        const { staffId: currentStaffId, staffName: currentStaffName } = getCurrentUserInfo(req);

        // Basic appointment data
        const baseAppointmentData = {
            petId: parseInt(petId),
            clientId: parseInt(clientId),
            clinicId: parseInt(clinicId) || req.user?.clinicId || 1001,
            staffInCharge: parseInt(staffInCharge) || currentStaffId,
            appointmentDate: new Date(appointmentDate),
            priority,
            status: 'in_progress',
            generalNotes,
            recommendations,
            createdBy: currentStaffId,
            appointmentCategories: [] // Will be populated below
        };

        // Process appointment categories and services
        if (appointmentCategories && appointmentCategories.length > 0) {
            for (const categoryData of appointmentCategories) {
                const processedServices = [];

                // Get category details for better prefilling
                const categoryDetails = await AppointmentCategory.findOne({
                    appointmentCategoryId: categoryData.appointmentCategoryId
                }).lean();

                // Determine assigned staff for this category (handle both field names)
                let assignedStaff = categoryData.staffAssigned || categoryData.assignedStaff || currentStaffId;
                let assignedStaffName = categoryData.staffAssignedName || categoryData.assignedStaffName || currentStaffName;

                // If category has default staff roles, try to find appropriate staff
                if (categoryDetails && categoryDetails.defaultStaffRoles && categoryDetails.defaultStaffRoles.length > 0) {
                    const availableStaff = await Staff.find({
                        clinicId: parseInt(clinicId) || req.user?.clinicId || 1001,
                        jobTitle: { $in: categoryDetails.defaultStaffRoles },
                        status: 1 // Active staff
                    }).lean();

                    if (availableStaff.length > 0) {
                        // Prefer the specified staff if they match the role, otherwise use first available
                        const matchingStaff = availableStaff.find(staff => staff.staffId === parseInt(staffInCharge)) || availableStaff[0];
                        assignedStaff = matchingStaff.staffId;
                        assignedStaffName = `${matchingStaff.firstName} ${matchingStaff.lastName}`.trim();
                    }
                }

                // Process services within this category
                if (categoryData.categoryServices && categoryData.categoryServices.length > 0) {
                    for (const serviceData of categoryData.categoryServices) {
                        // Find service by categoryServiceId
                        let service = await CategoryService.findOne({ categoryServiceId: serviceData.categoryServiceId }).lean();

                        if (!service) {
                            return sendResponse(res, 400, false, `Service ${serviceData.categoryServiceId} not found`);
                        }

                        processedServices.push({
                            categoryServiceId: service.categoryServiceId,
                            categoryServiceName: service.categoryServiceName,
                            price: serviceData.price || service.defaultPrice,
                            currency: service.currency || 'KES',
                            status: 'pending',
                            notes: serviceData.notes || '',
                            isCompleted: false,
                            estimatedDuration: service.estimatedDuration || categoryDetails?.estimatedDuration || 30
                            // Note: Staff assignment moved to category level
                        });
                    }
                }

                // Add category with services
                baseAppointmentData.appointmentCategories.push({
                    appointmentCategoryId: categoryData.appointmentCategoryId,
                    categoryName: categoryData.categoryName || categoryDetails?.name || 'Unknown Category',
                    categoryServices: processedServices,
                    categoryStatus: 'not_started',
                    isCompleted: false,
                    categoryNotes: categoryData.categoryNotes || '',
                    staffAssigned: assignedStaff,
                    staffAssignedName: assignedStaffName,
                    // Staff who will perform services in this category (initially same as assigned)
                    performedBy: assignedStaff,
                    performedByName: assignedStaffName,
                    // Staff who recorded this category (current user)
                    recordedBy: currentStaffId,
                    recordedByName: currentStaffName,
                    estimatedDuration: categoryDetails?.estimatedDuration || 30,
                    requiresEquipment: categoryDetails?.requiresEquipment || false,
                    requiresQualification: categoryDetails?.requiresQualification || false
                });
            }
        }

        // Populate denormalized data
        const enrichedAppointmentData = await populateDenormalizedData(baseAppointmentData);

        // Create appointment
        const appointment = await Appointment.create(enrichedAppointmentData);

        return sendResponse(res, 201, true, "Appointment created successfully", appointment);
    } catch (error) {
        console.error("Appointment creation error:", error);
        return sendResponse(res, 400, false, `Appointment creation failed: ${error.message}`);
    }
};

/**
 * Update an existing appointment with accurate prefilling
 */
const updateAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const {
            petId,
            clientId,
            clinicId,
            staffInCharge,
            appointmentDate,
            priority,
            status,
            appointmentCategories = [],
            generalNotes,
            recommendations,
            estimatedDuration
        } = req.body;

        const parsedAppointmentId = validateAppointmentId(appointmentId);
        const { staffId: currentStaffId } = getCurrentUserInfo(req);

        // Find existing appointment
        const existingAppointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!existingAppointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Prepare update data
        const updateData = {
            updatedBy: currentStaffId
        };

        // Update basic fields if provided
        if (petId) updateData.petId = parseInt(petId);
        if (clientId) updateData.clientId = parseInt(clientId);
        if (clinicId) updateData.clinicId = parseInt(clinicId);
        if (staffInCharge) updateData.staffInCharge = parseInt(staffInCharge);
        if (appointmentDate) updateData.appointmentDate = new Date(appointmentDate);
        if (priority) updateData.priority = priority;
        if (status) updateData.status = status;
        if (generalNotes !== undefined) updateData.generalNotes = generalNotes;
        if (recommendations !== undefined) updateData.recommendations = recommendations;
        if (estimatedDuration) updateData.estimatedDuration = parseInt(estimatedDuration);

        // Handle appointment categories update
        if (appointmentCategories && appointmentCategories.length > 0) {
            const processedCategories = [];

            for (const categoryData of appointmentCategories) {
                const processedServices = [];

                // Process services within this category
                if (categoryData.categoryServices && categoryData.categoryServices.length > 0) {
                    for (const serviceData of categoryData.categoryServices) {
                        // Check if this is an existing service (has an ID) or new service
                        if (serviceData.categoryServiceId) {
                            const service = await CategoryService.findOne({ categoryServiceId: serviceData.categoryServiceId }).lean();
                            if (service) {
                                processedServices.push({
                                    categoryServiceId: service.categoryServiceId,
                                    categoryServiceName: service.categoryServiceName,
                                    price: serviceData.price || service.defaultPrice,
                                    currency: service.currency || 'KES',
                                    status: serviceData.status || 'pending',
                                    notes: serviceData.notes || '',
                                    isCompleted: serviceData.isCompleted || false,
                                    startTime: serviceData.startTime,
                                    endTime: serviceData.endTime,
                                    completedAt: serviceData.completedAt
                                    // Note: Staff assignment moved to category level
                                });
                            }
                        }
                    }
                }

                // Add category with services
                processedCategories.push({
                    appointmentCategoryId: categoryData.appointmentCategoryId,
                    categoryName: categoryData.categoryName || 'Unknown Category',
                    categoryServices: processedServices,
                    categoryStatus: categoryData.categoryStatus || 'not_started',
                    isCompleted: categoryData.isCompleted || false,
                    categoryNotes: categoryData.categoryNotes || '',
                    completedAt: categoryData.completedAt
                });
            }

            updateData.appointmentCategories = processedCategories;
        }

        // If core fields are being updated, re-populate denormalized data
        if (petId || clientId || staffInCharge || clinicId) {
            const enrichedData = await populateDenormalizedData({
                ...existingAppointment.toObject(),
                ...updateData
            });

            // Merge the enriched denormalized data
            Object.assign(updateData, {
                petName: enrichedData.petName,
                petSpecies: enrichedData.petSpecies,
                petBreed: enrichedData.petBreed,
                petGender: enrichedData.petGender,
                petAge: enrichedData.petAge,
                petWeight: enrichedData.petWeight,
                clientName: enrichedData.clientName,
                clientPhone: enrichedData.clientPhone,
                clientEmail: enrichedData.clientEmail,
                staffInChargeName: enrichedData.staffInChargeName,
                staffInChargeJobTitle: enrichedData.staffInChargeJobTitle,
                clinicName: enrichedData.clinicName
            });
        }

        // Update appointment
        const updatedAppointment = await Appointment.findOneAndUpdate(
            { appointmentId: parsedAppointmentId },
            updateData,
            { new: true, runValidators: true }
        );

        return sendResponse(res, 200, true, "Appointment updated successfully", updatedAppointment);
    } catch (error) {
        console.error("Appointment update error:", error);
        return sendResponse(res, 400, false, `Appointment update failed: ${error.message}`);
    }
};

/**
 * Get all appointments with filtering
 */
const getAllNewAppointments = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "appointmentDate",
            sortOrder = "desc",
            status,
            priority,
            staffInCharge,
            clinicId,
            petId,
            clientId,
            startDate,
            endDate,
            completionStatus
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (status) query.status = status;
        if (priority) query.priority = priority;
        if (staffInCharge) query.staffInCharge = parseInt(staffInCharge);
        if (clinicId) query.clinicId = parseInt(clinicId);
        if (petId) query.petId = parseInt(petId);
        if (clientId) query.clientId = parseInt(clientId);
        if (completionStatus) query.completionStatus = completionStatus;

        // Date range filter
        if (startDate && endDate) {
            query.appointmentDate = {
                $gte: new Date(startDate),
                $lte: new Date(endDate)
            };
        } else if (startDate) {
            query.appointmentDate = { $gte: new Date(startDate) };
        } else if (endDate) {
            query.appointmentDate = { $lte: new Date(endDate) };
        }

        const [appointments, totalCount] = await Promise.all([
            Appointment.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Appointment.countDocuments(query)
        ]);

        // Get unique IDs for batch fetching
        const petIds = [...new Set(appointments.map(app => app.petId).filter(Boolean))];
        const clientIds = [...new Set(appointments.map(app => app.clientId).filter(Boolean))];
        const staffIds = [...new Set(appointments.map(app => app.staffInCharge).filter(Boolean))];

        // Fetch related data in parallel
        const [pets, clients, staffMembers] = await Promise.all([
            Pet.find({ petId: { $in: petIds } })
                .select('petId petName name')
                .lean(),
            Client.find({ clientId: { $in: clientIds } })
                .select('clientId firstName lastName email phoneNumber')
                .lean(),
            Staff.find({ staffId: { $in: staffIds } })
                .select('staffId firstName lastName')
                .lean()
        ]);

        // Create lookup maps for efficient data retrieval
        const petMap = new Map(pets.map(pet => [pet.petId, pet]));
        const clientMap = new Map(clients.map(client => [client.clientId, client]));
        const staffMap = new Map(staffMembers.map(staff => [staff.staffId, staff]));

        // Add computed fields and transform data
        const enrichedAppointments = appointments.map(appointment => {
            const serviceCategories = appointment.serviceCategories || [];

            // Extract pet and client data from maps
            const petData = petMap.get(appointment.petId);
            const clientData = clientMap.get(appointment.clientId);
            const staffData = staffMap.get(appointment.staffInCharge);

            return {
                ...appointment,
                // Add pet name for frontend compatibility
                petName: petData?.petName || petData?.name || 'Unknown Pet',
                petData: petData,
                // Add client name for frontend compatibility
                clientName: clientData ? `${clientData.firstName || ''} ${clientData.lastName || ''}`.trim() : 'Unknown Client',
                clientData: clientData,
                // Add staff name
                staffName: staffData ? `${staffData.firstName || ''} ${staffData.lastName || ''}`.trim() : 'Unknown Staff',
                staffData: staffData,
                // Computed fields
                totalServicesCount: serviceCategories.reduce((total, cat) =>
                    total + (cat.services ? cat.services.length : 0), 0),
                completedServicesCount: serviceCategories.reduce((total, cat) =>
                    total + (cat.services ? cat.services.filter(s => s.isCompleted).length : 0), 0),
                completionPercentage: serviceCategories.reduce((total, cat) =>
                    total + (cat.services ? cat.services.length : 0), 0) > 0
                    ? Math.round((serviceCategories.reduce((total, cat) =>
                        total + (cat.services ? cat.services.filter(s => s.isCompleted).length : 0), 0) /
                        serviceCategories.reduce((total, cat) =>
                            total + (cat.services ? cat.services.length : 0), 0)) * 100)
                    : 0
            };
        });

        return sendResponse(
            res,
            200,
            true,
            "Appointments retrieved successfully",
            paginateResults(enrichedAppointments, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get single appointment by ID
 */
const getNewAppointmentById = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const parsedAppointmentId = validateAppointmentId(appointmentId);

        const appointment = await Appointment.findOne({
            appointmentId: parsedAppointmentId
        }).lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // If appointment doesn't have denormalized data, fetch and populate it
        let enrichedAppointment = { ...appointment };

        if (!appointment.petName || !appointment.clientName || !appointment.staffInChargeName) {
            console.log('Appointment missing denormalized data, fetching related data...');

            // Fetch related data
            const [pet, client, staff] = await Promise.all([
                Pet.findOne({ petId: appointment.petId })
                    .select('petId petName name speciesId breedId gender weight dateOfBirth')
                    .populate('speciesId', 'speciesName')
                    .populate('breedId', 'breedName')
                    .lean(),
                Client.findOne({ clientId: appointment.clientId })
                    .select('clientId firstName lastName email phoneNumber')
                    .lean(),
                Staff.findOne({ staffId: appointment.staffInCharge })
                    .select('staffId firstName lastName jobTitle')
                    .lean()
            ]);

            // Add denormalized data if missing
            if (pet) {
                enrichedAppointment.petName = pet.petName || pet.name || 'Unknown Pet';
                enrichedAppointment.petSpecies = pet.speciesId?.speciesName || 'Dog';
                enrichedAppointment.petBreed = pet.breedId?.breedName || 'Mixed Breed';
                enrichedAppointment.petGender = pet.gender;
                enrichedAppointment.petWeight = pet.weight;
                if (pet.dateOfBirth) {
                    const ageInMonths = Math.floor((new Date() - new Date(pet.dateOfBirth)) / (1000 * 60 * 60 * 24 * 30.44));
                    enrichedAppointment.petAge = ageInMonths;
                }
            }

            if (client) {
                enrichedAppointment.clientName = `${client.firstName || ''} ${client.lastName || ''}`.trim();
                enrichedAppointment.clientPhone = client.phoneNumber;
                enrichedAppointment.clientEmail = client.email;
            }

            if (staff) {
                enrichedAppointment.staffInChargeName = `${staff.firstName || ''} ${staff.lastName || ''}`.trim();
                enrichedAppointment.staffInChargeJobTitle = staff.jobTitle;
            }
        }

        // Populate appointment category names if missing
        const appointmentCategories = enrichedAppointment.appointmentCategories || [];
        if (appointmentCategories.length > 0) {
            // Get unique category IDs that don't have names
            const categoryIdsToFetch = appointmentCategories
                .filter(cat => !cat.categoryName)
                .map(cat => cat.appointmentCategoryId);

            if (categoryIdsToFetch.length > 0) {
                const categoryDetails = await AppointmentCategory.find({
                    appointmentCategoryId: { $in: categoryIdsToFetch }
                }).select('appointmentCategoryId name').lean();

                // Add category names to appointment categories
                enrichedAppointment.appointmentCategories = appointmentCategories.map(cat => {
                    if (!cat.categoryName) {
                        const categoryDetail = categoryDetails.find(detail =>
                            detail.appointmentCategoryId === cat.appointmentCategoryId
                        );
                        return {
                            ...cat,
                            categoryName: categoryDetail?.name || `Category #${cat.appointmentCategoryId}`
                        };
                    }
                    return cat;
                });
            }
        }

        // Add computed fields for frontend compatibility
        const serviceCategories = enrichedAppointment.serviceCategories || [];

        // Calculate totals from both new and legacy structures
        const newStructureTotal = enrichedAppointment.appointmentCategories.reduce((total, cat) =>
            total + (cat.categoryServices ? cat.categoryServices.length : 0), 0);
        const legacyStructureTotal = serviceCategories.reduce((total, cat) =>
            total + (cat.services ? cat.services.length : 0), 0);

        const newStructureCompleted = enrichedAppointment.appointmentCategories.reduce((total, cat) =>
            total + (cat.categoryServices ? cat.categoryServices.filter(s => s.isCompleted).length : 0), 0);
        const legacyStructureCompleted = serviceCategories.reduce((total, cat) =>
            total + (cat.services ? cat.services.filter(s => s.isCompleted).length : 0), 0);

        const totalServicesCount = newStructureTotal + legacyStructureTotal;
        const completedServicesCount = newStructureCompleted + legacyStructureCompleted;

        enrichedAppointment.totalServicesCount = totalServicesCount;
        enrichedAppointment.completedServicesCount = completedServicesCount;
        enrichedAppointment.completionPercentage = totalServicesCount > 0
            ? Math.round((completedServicesCount / totalServicesCount) * 100)
            : 0;

        // Clean staff data structure - ensure staff info is only at category level
        enrichedAppointment.appointmentCategories.forEach(category => {
            if (category.categoryServices) {
                category.categoryServices.forEach(service => {
                    // Remove any service-level staff fields that might exist
                    delete service.performedBy;
                    delete service.performedByName;

                    // Add reference to category-level staff info for frontend convenience
                    service.categoryStaffInfo = {
                        assignedStaff: category.staffAssigned,
                        assignedStaffName: category.staffAssignedName,
                        performedBy: category.performedBy,
                        performedByName: category.performedByName,
                        recordedBy: category.recordedBy,
                        recordedByName: category.recordedByName
                    };
                });
            }
        });

        return sendResponse(res, 200, true, "Appointment retrieved successfully", enrichedAppointment);
    } catch (error) {
        console.error("Get appointment by ID error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Add service to appointment category
 */
const addServiceToAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { appointmentCategoryId, categoryServiceId, notes, price } = req.body;

        const parsedAppointmentId = validateAppointmentId(appointmentId);
        const { staffId: currentStaffId, staffName: currentStaffName } = getCurrentUserInfo(req);

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        const service = await CategoryService.findOne({ categoryServiceId: parseInt(categoryServiceId) }).lean();
        if (!service) {
            return sendResponse(res, 404, false, "Service not found");
        }

        // Find the category or create it
        let categoryIndex = appointment.appointmentCategories.findIndex(
            cat => cat.appointmentCategoryId === parseInt(appointmentCategoryId)
        );

        if (categoryIndex === -1) {
            return sendResponse(res, 404, false, "Appointment category not found in this appointment");
        }

        // Add service to category (staff assignment is at category level)
        const newService = {
            categoryServiceId: service.categoryServiceId,
            categoryServiceName: service.categoryServiceName,
            price: price || service.defaultPrice,
            currency: service.currency || 'KES',
            status: 'pending',
            notes: notes || '',
            isCompleted: false
            // Note: performedBy and performedByName are now at category level
        };

        // Update category with staff info if not already set
        const category = appointment.appointmentCategories[categoryIndex];
        if (!category.recordedBy) {
            category.recordedBy = currentStaffId;
            category.recordedByName = currentStaffName;
        }
        if (!category.performedBy) {
            category.performedBy = currentStaffId;
            category.performedByName = currentStaffName;
        }

        appointment.appointmentCategories[categoryIndex].categoryServices.push(newService);
        appointment.updatedBy = currentStaffId;

        await appointment.save();

        return sendResponse(res, 200, true, "Service added to appointment successfully", appointment);
    } catch (error) {
        console.error("Add service error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Complete a service in appointment
 */
const completeService = async (req, res) => {
    try {
        const { appointmentId, appointmentCategoryId, categoryServiceId } = req.params;
        const { notes, completionNotes } = req.body;

        const parsedAppointmentId = validateAppointmentId(appointmentId);
        const { staffId: currentStaffId } = getCurrentUserInfo(req);

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find category and service
        const category = appointment.appointmentCategories.find(
            cat => cat.appointmentCategoryId === parseInt(appointmentCategoryId)
        );
        if (!category) {
            return sendResponse(res, 404, false, "Appointment category not found");
        }

        const service = category.categoryServices.find(
            svc => svc.categoryServiceId === parseInt(categoryServiceId)
        );
        if (!service) {
            return sendResponse(res, 404, false, "Service not found in category");
        }

        // Mark service as completed
        service.status = 'completed';
        service.isCompleted = true;
        service.completedAt = new Date();
        service.endTime = new Date();
        if (notes) service.notes = notes;
        if (completionNotes) service.completionNotes = completionNotes;

        // Record who performed this service (only set performedBy when completing)
        // Don't duplicate staffAssigned - that should be set during assignment phase
        if (!category.performedBy || !category.performedByName) {
            const currentStaff = await Staff.findOne({ staffId: currentStaffId });
            if (currentStaff) {
                category.performedBy = currentStaffId;
                category.performedByName = `${currentStaff.firstName} ${currentStaff.lastName}`;
            }
        }

        appointment.updatedBy = currentStaffId;
        await appointment.save();

        return sendResponse(res, 200, true, "Service marked as completed", appointment);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Update appointment notes and recommendations
 */
const updateAppointmentNotes = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { generalNotes, recommendations } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOneAndUpdate(
            { appointmentId: parsedAppointmentId },
            {
                generalNotes,
                recommendations,
                updatedBy: req.user?.staffId || req.user?.userId
            },
            { new: true, runValidators: true }
        );

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        return sendResponse(res, 200, true, "Appointment notes updated successfully", appointment);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Assign categories to appointment with staff assignments
 */
const assignCategoriesToAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { selectedCategories, vetInCharge, clinicId } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        // Validate required fields
        if (!selectedCategories || !Array.isArray(selectedCategories) || selectedCategories.length === 0) {
            return sendResponse(res, 400, false, "Selected categories are required");
        }

        if (!vetInCharge) {
            return sendResponse(res, 400, false, "Veterinarian in charge is required");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Validate veterinarian ID
        if (!vetInCharge || isNaN(parseInt(vetInCharge))) {
            return sendResponse(res, 400, false, "Invalid veterinarian ID provided");
        }

        // Get veterinarian details
        const vet = await Staff.findOne({ staffId: parseInt(vetInCharge) });
        if (!vet) {
            return sendResponse(res, 404, false, "Veterinarian not found");
        }

        // Update appointment with vet in charge
        appointment.staffInCharge = vet.staffId;
        appointment.staffInChargeName = `${vet.firstName} ${vet.lastName}`;

        // Fix existing categories that might be missing required fields
        appointment.appointmentCategories.forEach(existingCategory => {
            if (!existingCategory.categoryName) {
                // Try to get category name from AppointmentCategory model
                const categoryDetails = AppointmentCategory.findOne({
                    appointmentCategoryId: existingCategory.appointmentCategoryId
                });
                existingCategory.categoryName = categoryDetails?.name || 'Unknown Category';
            }
            // Ensure other required fields exist
            if (!existingCategory.assignedStaff) existingCategory.assignedStaff = null;
            if (!existingCategory.assignedStaffName) existingCategory.assignedStaffName = '';
            if (!existingCategory.estimatedDuration) existingCategory.estimatedDuration = 30;
            if (!existingCategory.priority) existingCategory.priority = 'normal';
            if (existingCategory.requiresEquipment === undefined) existingCategory.requiresEquipment = false;
            if (existingCategory.requiresQualification === undefined) existingCategory.requiresQualification = false;
        });

        // Map category names to appointment category IDs
        const categoryMapping = {
            'consultation': 'Consultation',
            'vaccination': 'Vaccination',
            'laboratory': 'Laboratory',
            'surgery': 'Surgery',
            'grooming': 'Grooming',
            'dental': 'Dental',
            'emergency': 'Emergency',
            'wellness': 'Wellness Check',
            'therapy': 'Physical Therapy',
            'imaging': 'X-ray/Imaging',
            'behavioral': 'Behavioral Consultation',
            'nutrition': 'Nutrition Consultation',
            'medication': 'Medication',
            'other': 'Other'
        };

        // Process each selected category
        for (const categoryAssignment of selectedCategories) {
            const {
                appointmentCategoryId: categoryId,
                categoryName,
                staffAssigned,
                staffAssignedName,
                estimatedDuration,
                priority
            } = categoryAssignment;

            // Validate required fields
            if (!categoryId || isNaN(parseInt(categoryId))) {
                return sendResponse(res, 400, false, `Invalid category ID provided`);
            }

            if (!staffAssigned || isNaN(parseInt(staffAssigned))) {
                return sendResponse(res, 400, false, `Invalid staff ID provided for category ${categoryName}`);
            }

            // Get staff details for assigned staff
            const staffMember = await Staff.findOne({ staffId: parseInt(staffAssigned) });
            if (!staffMember) {
                return sendResponse(res, 404, false, `Staff member with ID ${staffAssigned} not found`);
            }

            // Verify the category exists
            const appointmentCategory = await AppointmentCategory.findOne({
                appointmentCategoryId: parseInt(categoryId)
            });

            if (!appointmentCategory) {
                return sendResponse(res, 404, false, `Category with ID ${categoryId} not found`);
            }

            const appointmentCategoryId = appointmentCategory.appointmentCategoryId;

            // Check if this category already exists in the appointment
            const existingCategoryIndex = appointment.appointmentCategories.findIndex(
                cat => cat.appointmentCategoryId === appointmentCategoryId
            );

            const categoryData = {
                appointmentCategoryId,
                categoryName: categoryName || appointmentCategory.name,
                staffAssigned: staffMember.staffId,
                staffAssignedName: staffAssignedName || `${staffMember.firstName} ${staffMember.lastName}`,
                // Staff who will perform services in this category (initially same as assigned)
                performedBy: staffMember.staffId,
                performedByName: `${staffMember.firstName} ${staffMember.lastName}`,
                // Staff who recorded this category assignment (current user)
                recordedBy: req.user?.staffId || req.user?.userId,
                recordedByName: req.user?.firstName ? `${req.user.firstName} ${req.user.lastName}` : 'Unknown',
                estimatedDuration: estimatedDuration || appointmentCategory?.estimatedDuration || 30,
                priority: priority || 'normal',
                requiresEquipment: appointmentCategory?.requiresEquipment || false,
                requiresQualification: appointmentCategory?.requiresQualification || false,
                categoryStatus: 'not_started',
                isCompleted: false,
                categoryServices: [],
                categoryNotes: ''
            };

            if (existingCategoryIndex >= 0) {
                // Update existing category - merge with existing data, preserving existing services
                const existingCategory = appointment.appointmentCategories[existingCategoryIndex];
                appointment.appointmentCategories[existingCategoryIndex] = {
                    ...existingCategory.toObject ? existingCategory.toObject() : existingCategory,
                    ...categoryData,
                    categoryServices: existingCategory.categoryServices || []
                };
            } else {
                // Add new category
                appointment.appointmentCategories.push(categoryData);
            }
        }

        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "Categories assigned successfully", appointment);
    } catch (error) {
        console.error("Assign categories error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Delete appointment category
 */
const deleteAppointmentCategory = async (req, res) => {
    try {
        const { appointmentId, appointmentCategoryId } = req.params;

        // Validate parameters
        const parsedAppointmentId = parseInt(appointmentId);
        const parsedCategoryId = parseInt(appointmentCategoryId);

        if (isNaN(parsedAppointmentId) || isNaN(parsedCategoryId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID or category ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find the category to delete
        const categoryIndex = appointment.appointmentCategories.findIndex(
            cat => cat.appointmentCategoryId === parsedCategoryId
        );

        if (categoryIndex === -1) {
            return sendResponse(res, 404, false, "Category not found in appointment");
        }

        // Remove the category
        appointment.appointmentCategories.splice(categoryIndex, 1);
        appointment.updatedBy = req.user?.staffId || req.user?.userId;

        await appointment.save();

        return sendResponse(res, 200, true, "Category removed successfully", appointment);
    } catch (error) {
        console.error("Delete appointment category error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Delete service from appointment category
 */
const deleteServiceFromAppointment = async (req, res) => {
    try {
        const { appointmentId, appointmentCategoryId, serviceId } = req.params;

        // Validate parameters
        const parsedAppointmentId = parseInt(appointmentId);
        const parsedCategoryId = parseInt(appointmentCategoryId);
        const parsedServiceId = parseInt(serviceId);

        if (isNaN(parsedAppointmentId) || isNaN(parsedCategoryId) || isNaN(parsedServiceId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID, category ID, or service ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find the category
        const categoryIndex = appointment.appointmentCategories.findIndex(
            cat => cat.appointmentCategoryId === parsedCategoryId
        );

        if (categoryIndex === -1) {
            return sendResponse(res, 404, false, "Category not found in appointment");
        }

        // Find and remove the service
        const category = appointment.appointmentCategories[categoryIndex];
        const serviceIndex = category.categoryServices.findIndex(
            service => (service.categoryServiceId || service.serviceId) === parsedServiceId
        );

        if (serviceIndex === -1) {
            return sendResponse(res, 404, false, "Service not found in category");
        }

        // Remove the service
        category.categoryServices.splice(serviceIndex, 1);
        appointment.updatedBy = req.user?.staffId || req.user?.userId;

        await appointment.save();

        return sendResponse(res, 200, true, "Service removed successfully", appointment);
    } catch (error) {
        console.error("Delete service from appointment error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Update appointment category (for notes, etc.)
 */
const updateAppointmentCategory = async (req, res) => {
    try {
        const { appointmentId, appointmentCategoryId } = req.params;
        const { categoryNotes, categoryStatus, isCompleted } = req.body;

        // Validate parameters
        const parsedAppointmentId = parseInt(appointmentId);
        const parsedCategoryId = parseInt(appointmentCategoryId);

        if (isNaN(parsedAppointmentId) || isNaN(parsedCategoryId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID or category ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find the category
        const category = appointment.appointmentCategories.find(
            cat => cat.appointmentCategoryId === parsedCategoryId
        );

        if (!category) {
            return sendResponse(res, 404, false, "Category not found in appointment");
        }

        // Update category fields
        if (categoryNotes !== undefined) category.categoryNotes = categoryNotes;
        if (categoryStatus !== undefined) category.categoryStatus = categoryStatus;
        if (isCompleted !== undefined) {
            category.isCompleted = isCompleted;
            if (isCompleted) {
                category.completedAt = new Date();
            }
        }

        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "Category updated successfully", appointment);
    } catch (error) {
        console.error("Update appointment category error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Update service in appointment category (for notes, etc.)
 */
const updateServiceInAppointment = async (req, res) => {
    try {
        const { appointmentId, appointmentCategoryId, serviceId } = req.params;
        const { notes, status, isCompleted, price } = req.body;

        // Validate parameters
        const parsedAppointmentId = parseInt(appointmentId);
        const parsedCategoryId = parseInt(appointmentCategoryId);
        const parsedServiceId = parseInt(serviceId);

        if (isNaN(parsedAppointmentId) || isNaN(parsedCategoryId) || isNaN(parsedServiceId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID, category ID, or service ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find the category
        const category = appointment.appointmentCategories.find(
            cat => cat.appointmentCategoryId === parsedCategoryId
        );

        if (!category) {
            return sendResponse(res, 404, false, "Category not found in appointment");
        }

        // Find the service
        const service = category.categoryServices.find(
            service => (service.categoryServiceId || service.serviceId) === parsedServiceId
        );

        if (!service) {
            return sendResponse(res, 404, false, "Service not found in category");
        }

        // Update service fields
        if (notes !== undefined) service.notes = notes;
        if (status !== undefined) service.status = status;
        if (price !== undefined) service.price = price;
        if (isCompleted !== undefined) {
            service.isCompleted = isCompleted;
            if (isCompleted) {
                service.completedAt = new Date();
            }
        }

        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "Service updated successfully", appointment);
    } catch (error) {
        console.error("Update service in appointment error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Assign staff to appointment category
 */
const assignStaffToAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { categoryId, staffId } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find the appointment type to assign staff to
        const appointmentTypeIndex = appointment.appointmentTypes.findIndex(
            type => type.categoryId === parseInt(categoryId)
        );

        if (appointmentTypeIndex === -1) {
            return sendResponse(res, 404, false, "Appointment type not found");
        }

        // Validate staff ID
        if (!staffId || isNaN(parseInt(staffId))) {
            return sendResponse(res, 400, false, "Invalid staff ID provided");
        }

        // Get staff information
        const Staff = (await import('../models/staff.model.js')).default;
        const staff = await Staff.findOne({ staffId: parseInt(staffId) }).lean();
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Assign staff to the appointment type
        appointment.appointmentTypes[appointmentTypeIndex].assignedStaff = staff.staffId;
        appointment.appointmentTypes[appointmentTypeIndex].assignedStaffName = `${staff.firstName} ${staff.lastName}`;
        appointment.updatedBy = req.user?.staffId || req.user?.userId;

        await appointment.save();

        return sendResponse(res, 200, true, "Staff assigned successfully", appointment);
    } catch (error) {
        console.error("Assign staff error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Update appointment status and completion
 */
const updateAppointmentStatus = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { status, completionStatus, generalNotes, recommendations } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const updateData = {
            updatedBy: req.user?.staffId || req.user?.userId
        };

        // Check if this is a complete endpoint call
        if (req.path.endsWith('/complete')) {
            updateData.status = 'completed';
            updateData.completionStatus = 'completed';
        } else {
            if (status) updateData.status = status;
            if (completionStatus) updateData.completionStatus = completionStatus;
        }

        if (generalNotes !== undefined) updateData.generalNotes = generalNotes;
        if (recommendations !== undefined) updateData.recommendations = recommendations;

        const appointment = await Appointment.findOneAndUpdate(
            { appointmentId: parsedAppointmentId },
            updateData,
            { new: true, runValidators: true }
        );

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        const message = req.path.endsWith('/complete')
            ? "Appointment marked as completed successfully"
            : "Appointment updated successfully";

        return sendResponse(res, 200, true, message, appointment);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Add service to appointment
 */
const addServiceToAppointmentWorkflow = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { categoryId, serviceName, price, currency, notes } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Get current user info with validation
        const currentUserId = req.user?.staffId || req.user?.userId;
        const currentUserName = req.user?.firstName ? `${req.user.firstName} ${req.user.lastName}` : 'Unknown';

        // Validate current user ID
        if (!currentUserId || isNaN(parseInt(currentUserId))) {
            return sendResponse(res, 400, false, "Invalid user ID in request");
        }

        // Create new service (staff assignment is now at category level)
        const newService = {
            categoryServiceId: Date.now(), // Simple ID generation
            categoryServiceName: serviceName,
            price: price || 0,
            currency: currency || 'KES',
            notes: notes || '',
            status: 'pending',
            isCompleted: false
            // Note: Staff assignment moved to category level
        };

        // Find or create appointment category
        let appointmentCategory = appointment.appointmentCategories?.find(cat => cat.appointmentCategoryId === categoryId);

        if (!appointmentCategory) {
            // Get category name from appointment types or use provided name
            const categoryName = serviceName.split(' - ')[0] || 'Unknown Category';

            appointmentCategory = {
                appointmentCategoryId: categoryId,
                categoryName: categoryName,
                categoryServices: [],
                categoryStatus: 'in_progress',
                isCompleted: false,
                // Staff assignment for this category
                staffAssigned: null, // Can be assigned later
                staffAssignedName: null,
                // Staff who performed services in this category
                performedBy: parseInt(currentUserId),
                performedByName: currentUserName,
                // Staff who recorded this category (staff in charge)
                recordedBy: parseInt(currentUserId),
                recordedByName: currentUserName
            };

            if (!appointment.appointmentCategories) {
                appointment.appointmentCategories = [];
            }
            appointment.appointmentCategories.push(appointmentCategory);
        } else {
            // Update existing category with staff info if not already set
            if (!appointmentCategory.recordedBy) {
                appointmentCategory.recordedBy = parseInt(currentUserId);
                appointmentCategory.recordedByName = currentUserName;
            }
            if (!appointmentCategory.performedBy) {
                appointmentCategory.performedBy = parseInt(currentUserId);
                appointmentCategory.performedByName = currentUserName;
            }
        }

        // Add service to category
        appointmentCategory.categoryServices.push(newService);

        // Update totals using appointmentCategories
        const totalServices = appointment.appointmentCategories.reduce((total, cat) =>
            total + (cat.categoryServices ? cat.categoryServices.length : 0), 0);
        const completedServices = appointment.appointmentCategories.reduce((total, cat) =>
            total + (cat.categoryServices ? cat.categoryServices.filter(s => s.isCompleted).length : 0), 0);

        appointment.totalServicesCount = totalServices;
        appointment.completedServicesCount = completedServices;
        appointment.completionPercentage = totalServices > 0 ? Math.round((completedServices / totalServices) * 100) : 0;

        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "Service added successfully", appointment);
    } catch (error) {
        console.error("Add service error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Add task to appointment
 */
const addTaskToAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { taskName, description, priority, assignedTo } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Get assigned staff information if provided
        let assignedToName = null;
        if (assignedTo) {
            // Validate staff ID
            if (isNaN(parseInt(assignedTo))) {
                return sendResponse(res, 400, false, "Invalid assigned staff ID provided");
            }

            const Staff = (await import('../models/staff.model.js')).default;
            const staff = await Staff.findOne({ staffId: parseInt(assignedTo) }).lean();
            if (staff) {
                assignedToName = `${staff.firstName} ${staff.lastName}`;
            }
        }

        // Create new task
        const newTask = {
            taskId: Date.now(), // Simple ID generation
            taskName: taskName,
            description: description || '',
            priority: priority || 'normal',
            status: 'pending',
            assignedTo: assignedTo ? parseInt(assignedTo) : null,
            assignedToName: assignedToName,
            createdBy: req.user?.staffId || req.user?.userId,
            createdAt: new Date()
        };

        // Add task to appointment
        if (!appointment.tasks) {
            appointment.tasks = [];
        }
        appointment.tasks.push(newTask);

        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "Task added successfully", appointment);
    } catch (error) {
        console.error("Add task error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Get appointment task status for progress tracking
 */
const getAppointmentTaskStatus = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId }).lean();
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Get staff information for assigned staff
        const Staff = (await import('../models/staff.model.js')).default;
        const staffMembers = await Staff.find({}).lean();
        const staffMap = {};
        staffMembers.forEach(staff => {
            staffMap[staff.staffId] = `${staff.firstName} ${staff.lastName}`;
        });

        // Process appointment categories to create selectedCategories for task status
        const selectedCategories = [];
        if (appointment.appointmentCategories && appointment.appointmentCategories.length > 0) {
            for (const category of appointment.appointmentCategories) {
                // Get assigned staff from category level (new structure)
                let assignedStaff = null;
                let assignedStaffName = null;

                // Check category-level staff assignment first
                if (category.staffAssigned) {
                    assignedStaff = category.staffAssigned;
                    assignedStaffName = category.staffAssignedName || staffMap[category.staffAssigned];
                } else if (category.performedBy) {
                    assignedStaff = category.performedBy;
                    assignedStaffName = category.performedByName || staffMap[category.performedBy];
                }

                // If no staff assigned at category level, fall back to appointment staff in charge
                if (!assignedStaff && appointment.staffInCharge) {
                    assignedStaff = appointment.staffInCharge;
                    assignedStaffName = appointment.staffInChargeName || staffMap[appointment.staffInCharge];
                }

                // Calculate estimated duration (sum of all services in category)
                let estimatedDuration = 30; // default
                if (category.categoryServices && category.categoryServices.length > 0) {
                    estimatedDuration = category.categoryServices.reduce((total, service) => {
                        return total + (service.duration || 15); // default 15 minutes per service
                    }, 0);
                }

                // Calculate actual duration if completed
                let actualDuration = null;
                if (category.isCompleted && category.categoryServices) {
                    actualDuration = category.categoryServices.reduce((total, service) => {
                        return total + (service.duration || 0);
                    }, 0);
                }

                selectedCategories.push({
                    category: category.categoryName.toLowerCase().replace(/\s+/g, '_'),
                    assignedStaff: assignedStaff || 0,
                    assignedStaffName: assignedStaffName || 'Unassigned',
                    isCompleted: category.isCompleted || false,
                    completedAt: category.completedAt,
                    completedBy: null, // Would need to track this separately
                    completedByName: null,
                    signature: null, // Would need to implement signature tracking
                    estimatedDuration: estimatedDuration,
                    actualDuration: actualDuration,
                    priority: 'normal' // Default priority, could be enhanced
                });
            }
        }

        // Calculate task summary
        const totalTasks = selectedCategories.length;
        const completedTasks = selectedCategories.filter(cat => cat.isCompleted).length;
        const pendingTasks = totalTasks - completedTasks;
        const completionPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        const totalEstimatedDuration = selectedCategories.reduce((total, cat) => total + cat.estimatedDuration, 0);
        const totalActualDuration = selectedCategories
            .filter(cat => cat.actualDuration)
            .reduce((total, cat) => total + cat.actualDuration, 0);

        const isFullyCompleted = totalTasks > 0 && completedTasks === totalTasks;

        const taskSummary = {
            totalTasks,
            completedTasks,
            pendingTasks,
            completionPercentage,
            totalEstimatedDuration,
            totalActualDuration,
            isFullyCompleted
        };

        // Prepare appointment data with selectedCategories
        const appointmentWithTasks = {
            ...appointment,
            selectedCategories
        };

        return sendResponse(res, 200, true, "Task status retrieved successfully", {
            appointment: appointmentWithTasks,
            taskSummary
        });
    } catch (error) {
        console.error("Get appointment task status error:", error);
        return sendResponse(res, 500, false, `Failed to get task status: ${error.message}`);
    }
};

/**
 * Get current appointments (in progress)
 */
const getCurrentAppointments = async (req, res) => {
    try {
        const { clinicId, staffId } = req.query;

        let query = {
            status: { $in: ['in_progress', 'scheduled'] },
            appointmentDate: {
                $gte: new Date(new Date().setHours(0, 0, 0, 0)), // Today
                $lte: new Date(new Date().setHours(23, 59, 59, 999))
            }
        };

        if (clinicId) query.clinicId = parseInt(clinicId);
        if (staffId) query.staffInCharge = parseInt(staffId);

        const appointments = await Appointment.find(query)
            .sort({ appointmentDate: 1 })
            .lean();

        // Get unique IDs for batch fetching
        const petIds = [...new Set(appointments.map(app => app.petId).filter(Boolean))];
        const clientIds = [...new Set(appointments.map(app => app.clientId).filter(Boolean))];
        const staffIds = [...new Set(appointments.map(app => app.staffInCharge).filter(Boolean))];

        // Fetch related data in parallel
        const [pets, clients, staffMembers] = await Promise.all([
            Pet.find({ petId: { $in: petIds } })
                .select('petId petName name')
                .lean(),
            Client.find({ clientId: { $in: clientIds } })
                .select('clientId firstName lastName email phoneNumber')
                .lean(),
            Staff.find({ staffId: { $in: staffIds } })
                .select('staffId firstName lastName')
                .lean()
        ]);

        // Create lookup maps for efficient data retrieval
        const petMap = new Map(pets.map(pet => [pet.petId, pet]));
        const clientMap = new Map(clients.map(client => [client.clientId, client]));
        const staffMap = new Map(staffMembers.map(staff => [staff.staffId, staff]));

        // Add computed fields and transform data
        const enrichedAppointments = appointments.map(appointment => {
            const serviceCategories = appointment.serviceCategories || [];

            // Extract pet and client data from maps
            const petData = petMap.get(appointment.petId);
            const clientData = clientMap.get(appointment.clientId);
            const staffData = staffMap.get(appointment.staffInCharge);

            return {
                ...appointment,
                // Add pet name for frontend compatibility
                petName: petData?.petName || petData?.name || 'Unknown Pet',
                petData: petData,
                // Add client name for frontend compatibility
                clientName: clientData ? `${clientData.firstName || ''} ${clientData.lastName || ''}`.trim() : 'Unknown Client',
                clientData: clientData,
                // Add staff name
                staffName: staffData ? `${staffData.firstName || ''} ${staffData.lastName || ''}`.trim() : 'Unknown Staff',
                staffData: staffData,
                // Computed fields
                totalServicesCount: serviceCategories.reduce((total, cat) =>
                    total + (cat.services ? cat.services.length : 0), 0),
                completedServicesCount: serviceCategories.reduce((total, cat) =>
                    total + (cat.services ? cat.services.filter(s => s.isCompleted).length : 0), 0),
                completionPercentage: serviceCategories.reduce((total, cat) =>
                    total + (cat.services ? cat.services.length : 0), 0) > 0
                    ? Math.round((serviceCategories.reduce((total, cat) =>
                        total + (cat.services ? cat.services.filter(s => s.isCompleted).length : 0), 0) /
                        serviceCategories.reduce((total, cat) =>
                            total + (cat.services ? cat.services.length : 0), 0)) * 100)
                    : 0
            };
        });

        return sendResponse(res, 200, true, "Current appointments retrieved successfully", enrichedAppointments);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Create a follow-up appointment
 */
const createFollowUpAppointment = async (req, res) => {
    try {
        const { parentAppointmentId } = req.params;
        const { appointmentDate, staffInCharge, priority, reason, type, notes } = req.body;

        // Import follow-up manager
        const { createFollowUpAppointment: createFollowUp } = await import('../utils/followUpManager.js');

        const followUpData = {
            appointmentDate: new Date(appointmentDate),
            staffInCharge,
            priority,
            reason,
            type,
            notes
        };

        const currentStaffId = req.user?.staffId || req.user?.userId;
        const followUpAppointment = await createFollowUp(
            parseInt(parentAppointmentId),
            followUpData,
            currentStaffId
        );

        return sendResponse(res, 201, true, "Follow-up appointment created successfully", followUpAppointment);
    } catch (error) {
        console.error("Follow-up appointment creation error:", error);
        return sendResponse(res, 400, false, `Follow-up creation failed: ${error.message}`);
    }
};

/**
 * Schedule a follow-up reminder
 */
const scheduleFollowUpReminder = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { followUpDate, instructions, priority } = req.body;

        const { scheduleFollowUpReminder: scheduleReminder } = await import('../utils/followUpManager.js');

        const followUpData = {
            followUpDate: new Date(followUpDate),
            instructions,
            priority
        };

        const updatedAppointment = await scheduleReminder(parseInt(appointmentId), followUpData);

        return sendResponse(res, 200, true, "Follow-up reminder scheduled successfully", updatedAppointment);
    } catch (error) {
        console.error("Follow-up reminder scheduling error:", error);
        return sendResponse(res, 400, false, `Follow-up reminder scheduling failed: ${error.message}`);
    }
};

/**
 * Get appointment chain (parent + follow-ups)
 */
const getAppointmentChain = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        const { getAppointmentChain: getChain } = await import('../utils/followUpManager.js');
        const appointmentChain = await getChain(parseInt(appointmentId));

        return sendResponse(res, 200, true, "Appointment chain retrieved successfully", appointmentChain);
    } catch (error) {
        console.error("Get appointment chain error:", error);
        return sendResponse(res, 400, false, `Failed to get appointment chain: ${error.message}`);
    }
};

/**
 * Get overdue follow-ups for clinic
 */
const getOverdueFollowUps = async (req, res) => {
    try {
        const { clinicId } = req.query;
        const daysOverdue = parseInt(req.query.daysOverdue) || 0;

        const { getOverdueFollowUps: getOverdue } = await import('../utils/followUpManager.js');
        const overdueAppointments = await getOverdue(
            parseInt(clinicId) || req.user?.clinicId,
            daysOverdue
        );

        return sendResponse(res, 200, true, "Overdue follow-ups retrieved successfully", overdueAppointments);
    } catch (error) {
        console.error("Get overdue follow-ups error:", error);
        return sendResponse(res, 400, false, `Failed to get overdue follow-ups: ${error.message}`);
    }
};

/**
 * Get upcoming follow-ups for clinic
 */
const getUpcomingFollowUps = async (req, res) => {
    try {
        const { clinicId } = req.query;
        const daysAhead = parseInt(req.query.daysAhead) || 7;

        const { getUpcomingFollowUps: getUpcoming } = await import('../utils/followUpManager.js');
        const upcomingAppointments = await getUpcoming(
            parseInt(clinicId) || req.user?.clinicId,
            daysAhead
        );

        return sendResponse(res, 200, true, "Upcoming follow-ups retrieved successfully", upcomingAppointments);
    } catch (error) {
        console.error("Get upcoming follow-ups error:", error);
        return sendResponse(res, 400, false, `Failed to get upcoming follow-ups: ${error.message}`);
    }
};

/**
 * Get all appointment categories
 */
const getAppointmentCategories = async (req, res) => {
    try {
        const { clinicId, isActive = true } = req.query;

        let query = { isActive: isActive === 'true' };

        // Include global categories and clinic-specific categories
        if (clinicId) {
            query.$or = [
                { clinicId: null }, // Global categories
                { clinicId: parseInt(clinicId) } // Clinic-specific categories
            ];
        } else {
            query.clinicId = null; // Only global categories if no clinic specified
        }

        const categories = await AppointmentCategory.find(query)
            .sort({ displayOrder: 1, name: 1 })
            .lean();

        // Ensure charge and discount fields are included
        const categoriesWithCharges = categories.map(category => ({
            ...category,
            defaultCharge: category.defaultCharge || 0,
            currency: category.currency || 'KES',
            defaultDiscountPercentage: category.defaultDiscountPercentage || 0
        }));

        return sendResponse(res, 200, true, "Appointment categories retrieved successfully", categoriesWithCharges);
    } catch (error) {
        console.error("Get appointment categories error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Get services for a specific appointment category
 */
const getCategoryServices = async (req, res) => {
    try {
        const { appointmentCategoryId } = req.params;
        const { clinicId, isActive = true } = req.query;

        let query = {
            appointmentCategoryId: parseInt(appointmentCategoryId),
            isActive: isActive === 'true'
        };

        // Include global services and clinic-specific services
        if (clinicId) {
            query.$or = [
                { clinicId: null }, // Global services
                { clinicId: parseInt(clinicId) } // Clinic-specific services
            ];
        } else {
            query.clinicId = null; // Only global services if no clinic specified
        }

        const services = await CategoryService.find(query)
            .sort({ categoryServiceName: 1 })
            .lean();

        return sendResponse(res, 200, true, "Category services retrieved successfully", services);
    } catch (error) {
        console.error("Get category services error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Complete an entire appointment category (for task status)
 */
const completeAppointmentCategory = async (req, res) => {
    try {
        const { appointmentId, category } = req.params;
        const { signature, actualDuration, notes } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find the category by name (convert category parameter to match stored format)
        const categoryName = category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        const appointmentCategory = appointment.appointmentCategories.find(
            cat => cat.categoryName.toLowerCase() === categoryName.toLowerCase()
        );

        if (!appointmentCategory) {
            return sendResponse(res, 404, false, `Category "${categoryName}" not found in appointment`);
        }

        // Mark category as completed
        appointmentCategory.isCompleted = true;
        appointmentCategory.completedAt = new Date();
        appointmentCategory.categoryStatus = 'completed';

        // Record who performed this category completion (only set performedBy when completing)
        // Don't duplicate staffAssigned - that should be set during assignment phase
        const currentStaffId = req.user?.staffId || req.user?.userId;
        if (!appointmentCategory.performedBy || !appointmentCategory.performedByName) {
            const Staff = mongoose.model('Staff');
            const currentStaff = await Staff.findOne({ staffId: currentStaffId });
            if (currentStaff) {
                appointmentCategory.performedBy = currentStaffId;
                appointmentCategory.performedByName = `${currentStaff.firstName} ${currentStaff.lastName}`;
            }
        }

        // Also record who performed this category
        if (!appointmentCategory.performedBy || !appointmentCategory.performedByName) {
            const Staff = mongoose.model('Staff');
            const currentStaff = await Staff.findOne({ staffId: currentStaffId });
            if (currentStaff) {
                appointmentCategory.performedBy = currentStaffId;
                appointmentCategory.performedByName = `${currentStaff.firstName} ${currentStaff.lastName}`;
            }
        }

        // Add completion notes if provided
        if (notes) {
            appointmentCategory.categoryNotes = appointmentCategory.categoryNotes
                ? `${appointmentCategory.categoryNotes}\n\nCompletion Notes: ${notes}`
                : `Completion Notes: ${notes}`;
        }

        // Mark all services in the category as completed if they aren't already
        if (appointmentCategory.categoryServices) {
            appointmentCategory.categoryServices.forEach(service => {
                if (!service.isCompleted) {
                    service.isCompleted = true;
                    service.status = 'completed';
                    service.completedAt = new Date();
                    service.endTime = new Date();
                    if (actualDuration) {
                        service.duration = Math.round(actualDuration / appointmentCategory.categoryServices.length);
                    }
                }
            });
        }

        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "Category completed successfully", appointment);
    } catch (error) {
        console.error("Complete appointment category error:", error);
        return sendResponse(res, 500, false, `Failed to complete category: ${error.message}`);
    }
};

/**
 * Generate appointment summary
 */
const generateAppointmentSummary = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Generate summary based on completed services and categories
        let summaryNotes = "Appointment Summary:\n\n";

        if (appointment.appointmentCategories && appointment.appointmentCategories.length > 0) {
            summaryNotes += "Completed Categories:\n";

            appointment.appointmentCategories.forEach(category => {
                if (category.isCompleted) {
                    summaryNotes += `- ${category.categoryName}\n`;

                    if (category.categoryServices && category.categoryServices.length > 0) {
                        const completedServices = category.categoryServices.filter(s => s.isCompleted);
                        if (completedServices.length > 0) {
                            summaryNotes += `  Services: ${completedServices.map(s => s.serviceName).join(', ')}\n`;
                        }
                    }

                    if (category.categoryNotes) {
                        summaryNotes += `  Notes: ${category.categoryNotes}\n`;
                    }
                    summaryNotes += "\n";
                }
            });
        }

        // Add general notes if available
        if (appointment.generalNotes) {
            summaryNotes += `General Notes: ${appointment.generalNotes}\n\n`;
        }

        // Add recommendations if available
        if (appointment.recommendations) {
            summaryNotes += `Recommendations: ${appointment.recommendations}\n\n`;
        }

        // Calculate completion statistics
        const totalCategories = appointment.appointmentCategories?.length || 0;
        const completedCategories = appointment.appointmentCategories?.filter(cat => cat.isCompleted).length || 0;
        const completionPercentage = totalCategories > 0 ? Math.round((completedCategories / totalCategories) * 100) : 0;

        summaryNotes += `Completion Status: ${completedCategories}/${totalCategories} categories completed (${completionPercentage}%)\n`;
        summaryNotes += `Generated on: ${new Date().toLocaleString()}\n`;

        // Update appointment with generated summary
        appointment.generalNotes = appointment.generalNotes
            ? `${appointment.generalNotes}\n\n--- AUTO-GENERATED SUMMARY ---\n${summaryNotes}`
            : summaryNotes;

        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "Summary generated successfully", {
            appointment,
            summary: summaryNotes,
            completionStats: {
                totalCategories,
                completedCategories,
                completionPercentage
            }
        });
    } catch (error) {
        console.error("Generate appointment summary error:", error);
        return sendResponse(res, 500, false, `Failed to generate summary: ${error.message}`);
    }
};

/**
 * Generate AI appointment notes from all services and categories
 */
const generateAIAppointmentNotes = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { context = 'comprehensive_appointment_summary' } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Collect all notes from services and categories
        const allNotes = {
            categories: [],
            services: [],
            generalNotes: appointment.generalNotes || '',
            recommendations: appointment.recommendations || ''
        };

        if (appointment.appointmentCategories?.length > 0) {
            appointment.appointmentCategories.forEach(category => {
                // Add category notes
                if (category.categoryNotes) {
                    allNotes.categories.push({
                        categoryName: category.categoryName,
                        notes: category.categoryNotes,
                        status: category.categoryStatus,
                        staffAssigned: category.staffAssignedName,
                        performedBy: category.performedByName,
                        recordedBy: category.recordedByName
                    });
                }

                // Add service notes (staff info now comes from category level)
                if (category.categoryServices?.length > 0) {
                    category.categoryServices.forEach(service => {
                        if (service.notes) {
                            allNotes.services.push({
                                serviceName: service.categoryServiceName,
                                categoryName: category.categoryName,
                                notes: service.notes,
                                status: service.status,
                                performedBy: category.performedByName, // From category level
                                recordedBy: category.recordedByName,   // From category level
                                price: service.price,
                                currency: service.currency
                            });
                        }
                    });
                }
            });
        }

        // Generate AI summary using a simple template (replace with actual AI service)
        const aiGeneratedNotes = generateComprehensiveAppointmentNotes(appointment, allNotes);

        // Update appointment with AI generated notes
        appointment.aiGeneratedSummary = aiGeneratedNotes;
        appointment.aiGeneratedAt = new Date();
        appointment.updatedBy = req.user?.staffId || req.user?.userId;
        await appointment.save();

        return sendResponse(res, 200, true, "AI appointment notes generated successfully", {
            appointmentId: appointment.appointmentId,
            aiGeneratedNotes,
            generatedAt: new Date().toISOString(),
            sourceData: allNotes
        });
    } catch (error) {
        console.error("Generate AI appointment notes error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Helper function to generate comprehensive appointment notes
 */
const generateComprehensiveAppointmentNotes = (appointment, allNotes) => {
    let summary = `COMPREHENSIVE APPOINTMENT SUMMARY\n\n`;

    // Basic appointment info
    summary += `Patient: ${appointment.petName} (${appointment.petSpecies})\n`;
    summary += `Client: ${appointment.clientName}\n`;
    summary += `Date: ${new Date(appointment.appointmentDate).toLocaleDateString()}\n`;
    summary += `Veterinarian in Charge: ${appointment.staffInChargeName}\n`;
    summary += `Appointment Status: ${appointment.status}\n\n`;

    // Categories and services summary
    if (allNotes.categories.length > 0 || allNotes.services.length > 0) {
        summary += `SERVICES AND PROCEDURES PERFORMED:\n\n`;

        // Group services by category
        const servicesByCategory = {};
        allNotes.services.forEach(service => {
            if (!servicesByCategory[service.categoryName]) {
                servicesByCategory[service.categoryName] = [];
            }
            servicesByCategory[service.categoryName].push(service);
        });

        // Add category summaries
        allNotes.categories.forEach(category => {
            summary += `${category.categoryName.toUpperCase()}:\n`;
            summary += `Status: ${category.status}\n`;
            if (category.staffAssigned) {
                summary += `Assigned Staff: ${category.staffAssigned}\n`;
            }
            if (category.notes) {
                summary += `Category Notes: ${category.notes}\n`;
            }

            // Add services for this category
            if (servicesByCategory[category.categoryName]) {
                servicesByCategory[category.categoryName].forEach(service => {
                    summary += `  • ${service.serviceName} (${service.currency} ${service.price})\n`;
                    summary += `    Performed by: ${service.performedBy}\n`;
                    summary += `    Status: ${service.status}\n`;
                    summary += `    Notes: ${service.notes}\n`;
                });
            }
            summary += `\n`;
        });

        // Add any services not covered by categories
        const coveredCategories = allNotes.categories.map(c => c.categoryName);
        allNotes.services.forEach(service => {
            if (!coveredCategories.includes(service.categoryName)) {
                summary += `${service.categoryName.toUpperCase()}:\n`;
                summary += `  • ${service.serviceName} (${service.currency} ${service.price})\n`;
                summary += `    Performed by: ${service.performedBy}\n`;
                summary += `    Status: ${service.status}\n`;
                summary += `    Notes: ${service.notes}\n\n`;
            }
        });
    }

    // General notes and recommendations
    if (allNotes.generalNotes) {
        summary += `GENERAL NOTES:\n${allNotes.generalNotes}\n\n`;
    }

    if (allNotes.recommendations) {
        summary += `RECOMMENDATIONS:\n${allNotes.recommendations}\n\n`;
    }

    // Calculate total charges
    const totalCharges = allNotes.services.reduce((total, service) => total + (service.price || 0), 0);
    const currency = allNotes.services.length > 0 ? allNotes.services[0].currency : 'KES';

    summary += `FINANCIAL SUMMARY:\n`;
    summary += `Total Charges: ${currency} ${totalCharges}\n\n`;

    // Completion status
    const completedServices = allNotes.services.filter(s => s.status === 'completed').length;
    const totalServices = allNotes.services.length;
    const completionPercentage = totalServices > 0 ? Math.round((completedServices / totalServices) * 100) : 0;

    summary += `APPOINTMENT COMPLETION:\n`;
    summary += `Services Completed: ${completedServices}/${totalServices} (${completionPercentage}%)\n`;
    summary += `Overall Status: ${appointment.status}\n\n`;

    summary += `Summary generated on: ${new Date().toLocaleString()}\n`;
    summary += `Generated by: Vet-Care AI Assistant\n`;

    return summary;
};

/**
 * Migrate existing appointments to move staff assignment from service level to category level
 */
const migrateStaffAssignments = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        let query = {};
        if (appointmentId) {
            const parsedAppointmentId = parseInt(appointmentId);
            if (isNaN(parsedAppointmentId)) {
                return sendResponse(res, 400, false, "Invalid appointment ID provided");
            }
            query.appointmentId = parsedAppointmentId;
        }

        const appointments = await Appointment.find(query);
        let migratedCount = 0;

        for (const appointment of appointments) {
            let needsUpdate = false;

            if (appointment.appointmentCategories?.length > 0) {
                appointment.appointmentCategories.forEach(category => {
                    // If category is completed but has no performedBy, set it to main staff
                    if (category.isCompleted && !category.performedBy) {
                        category.performedBy = appointment.staffInCharge;
                        category.performedByName = appointment.staffInChargeName;
                        needsUpdate = true;
                    }

                    // If category is not completed but has no staffAssigned, set it to main staff
                    if (!category.isCompleted && !category.staffAssigned) {
                        category.staffAssigned = appointment.staffInCharge;
                        category.staffAssignedName = appointment.staffInChargeName;
                        needsUpdate = true;
                    }

                    if (category.categoryServices?.length > 0) {
                        // Check if any service has staff assignment that needs to be moved to category
                        const servicesWithStaff = category.categoryServices.filter(service =>
                            service.performedBy || service.performedByName || service.recordedBy || service.recordedByName
                        );

                        if (servicesWithStaff.length > 0) {
                            // Move staff assignment from first service to category level
                            const firstServiceWithStaff = servicesWithStaff[0];

                            if (!category.performedBy && firstServiceWithStaff.performedBy) {
                                category.performedBy = firstServiceWithStaff.performedBy;
                                category.performedByName = firstServiceWithStaff.performedByName;
                                needsUpdate = true;
                            }

                            if (!category.recordedBy && firstServiceWithStaff.recordedBy) {
                                category.recordedBy = firstServiceWithStaff.recordedBy;
                                category.recordedByName = firstServiceWithStaff.recordedByName;
                                needsUpdate = true;
                            }

                            // If no staffAssigned but we have performedBy, copy it
                            if (!category.staffAssigned && category.performedBy) {
                                category.staffAssigned = category.performedBy;
                                category.staffAssignedName = category.performedByName;
                                needsUpdate = true;
                            }

                            // Remove staff fields from all services in this category
                            category.categoryServices.forEach(service => {
                                if (service.performedBy || service.performedByName || service.recordedBy || service.recordedByName) {
                                    delete service.performedBy;
                                    delete service.performedByName;
                                    delete service.recordedBy;
                                    delete service.recordedByName;
                                    needsUpdate = true;
                                }
                            });
                        }
                    }
                });
            }

            if (needsUpdate) {
                await appointment.save();
                migratedCount++;
            }
        }

        return sendResponse(res, 200, true, `Migration completed. ${migratedCount} appointments updated.`, {
            migratedCount,
            totalProcessed: appointments.length
        });
    } catch (error) {
        console.error("Staff assignment migration error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Generate medical record from completed appointment
 */
const generateMedicalRecord = async (req, res) => {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        const { appointmentId } = req.params;
        const { recordType = 'appointment_summary' } = req.body;

        // Validate appointmentId parameter
        const parsedAppointmentId = parseInt(appointmentId);
        if (isNaN(parsedAppointmentId)) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 400, false, "Invalid appointment ID provided");
        }

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId }).session(session);
        if (!appointment) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Check if appointment is completed
        if (appointment.completionStatus !== 'completed') {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 400, false, "Cannot generate medical record for incomplete appointment");
        }

        // Check if medical record already exists for this appointment
        const HealthRecord = mongoose.model('HealthRecord');
        const existingRecord = await HealthRecord.findOne({ appointmentId: parsedAppointmentId }).session(session);
        if (existingRecord) {
            await session.abortTransaction();
            await session.endSession();
            return sendResponse(res, 400, false, "Medical record already exists for this appointment", existingRecord);
        }

        // Determine if this is a new visit or continuation
        const previousRecords = await HealthRecord.find({
            petId: appointment.petId,
            clinicId: appointment.clinicId
        }).sort({ date: -1 }).limit(1).session(session);

        const isNewVisit = previousRecords.length === 0 ||
            (new Date() - new Date(previousRecords[0].date)) > (30 * 24 * 60 * 60 * 1000); // 30 days

        // Collect all medications used in the appointment
        const medicationsUsed = [];
        appointment.appointmentCategories.forEach(category => {
            if (category.categoryServices) {
                category.categoryServices.forEach(service => {
                    if (service.medicationsUsed && service.medicationsUsed.length > 0) {
                        service.medicationsUsed.forEach(medication => {
                            medicationsUsed.push({
                                name: medication.medicationName,
                                dosage: medication.dosage || '',
                                frequency: medication.frequency || '',
                                duration: medication.duration || '',
                                notes: medication.instructions || '',
                                // Staff info comes from category level, not service level
                                prescribedBy: category.performedBy || category.staffAssigned || appointment.staffInCharge,
                                prescribedByName: category.performedByName || category.staffAssignedName || appointment.staffInChargeName
                            });
                        });
                    }
                });
            }
        });

        // Collect all services performed
        const servicesPerformed = [];
        appointment.appointmentCategories.forEach(category => {
            if (category.categoryServices) {
                category.categoryServices.forEach(service => {
                    if (service.isCompleted) {
                        servicesPerformed.push({
                            categoryName: category.categoryName,
                            serviceName: service.categoryServiceName,
                            notes: service.notes || '',
                            completedAt: service.completedAt,
                            // Staff info comes from category level, not service level
                            performedBy: category.performedBy || category.staffAssigned || appointment.staffInCharge,
                            performedByName: category.performedByName || category.staffAssignedName || appointment.staffInChargeName,
                            assignedStaff: category.staffAssigned,
                            assignedStaffName: category.staffAssignedName
                        });
                    }
                });
            }
        });

        // Create comprehensive medical record
        const medicalRecordData = {
            petId: appointment.petId,
            clinicId: appointment.clinicId,
            appointmentId: appointment.appointmentId,
            performedBy: appointment.staffInCharge,
            recordType: recordType,
            date: new Date(),

            // Visit information
            visitType: isNewVisit ? 'new_visit' : 'follow_up',
            chiefComplaint: appointment.generalNotes || 'Routine appointment',

            // Clinical findings
            clinicalFindings: appointment.recommendations || '',

            // Medications prescribed
            medications: medicationsUsed,

            // Services and procedures
            procedures: servicesPerformed.map(service => ({
                name: service.serviceName,
                category: service.categoryName,
                notes: service.notes,
                performedBy: service.performedBy,
                performedByName: service.performedByName,
                assignedStaff: service.assignedStaff,
                assignedStaffName: service.assignedStaffName
            })),

            // Billing information
            billingDetails: {
                amount: appointment.billing.totalAmount,
                currency: appointment.billing.currency,
                paymentStatus: appointment.billing.paymentStatus
            },

            // Follow-up information
            followUpRequired: appointment.followUp?.hasScheduledFollowUps || false,
            followUpDate: appointment.followUp?.nextFollowUpDate || null,

            // Additional notes
            notes: appointment.aiGeneratedSummary || generateMedicalRecordSummary(appointment),

            // Access control
            accessControl: {
                isPublic: true, // Allow other clinics to view
                restrictedFields: []
            }
        };

        // Create the medical record
        const [medicalRecord] = await HealthRecord.create([medicalRecordData], { session });

        await session.commitTransaction();
        await session.endSession();

        return sendResponse(res, 201, true, "Medical record generated successfully", {
            medicalRecord,
            visitType: isNewVisit ? 'new_visit' : 'follow_up',
            medicationsCount: medicationsUsed.length,
            servicesCount: servicesPerformed.length
        });

    } catch (error) {
        await session.abortTransaction();
        await session.endSession();
        console.error("Generate medical record error:", error);
        return sendResponse(res, 500, false, `Failed to generate medical record: ${error.message}`);
    }
};

/**
 * Helper function to generate medical record summary
 */
function generateMedicalRecordSummary(appointment) {
    let summary = `MEDICAL RECORD SUMMARY\n`;
    summary += `=====================\n\n`;
    summary += `Patient: ${appointment.petName} (${appointment.petSpecies}, ${appointment.petBreed})\n`;
    summary += `Age: ${appointment.petAge} months, Weight: ${appointment.petWeight}kg\n`;
    summary += `Owner: ${appointment.clientName}\n`;
    summary += `Date: ${new Date(appointment.appointmentDate).toLocaleDateString()}\n`;
    summary += `Attending Veterinarian: ${appointment.staffInChargeName}\n\n`;

    // Add completed services
    summary += `SERVICES PERFORMED:\n`;
    summary += `------------------\n`;
    appointment.appointmentCategories.forEach(category => {
        if (category.categoryServices) {
            const completedServices = category.categoryServices.filter(s => s.isCompleted);
            if (completedServices.length > 0) {
                summary += `${category.categoryName}:\n`;
                completedServices.forEach(service => {
                    summary += `  - ${service.categoryServiceName}\n`;
                    if (service.notes) summary += `    Notes: ${service.notes}\n`;
                });
                summary += `\n`;
            }
        }
    });

    if (appointment.generalNotes) {
        summary += `CLINICAL NOTES:\n`;
        summary += `--------------\n`;
        summary += `${appointment.generalNotes}\n\n`;
    }

    if (appointment.recommendations) {
        summary += `RECOMMENDATIONS:\n`;
        summary += `---------------\n`;
        summary += `${appointment.recommendations}\n\n`;
    }

    summary += `Total Amount: ${appointment.billing.currency} ${appointment.billing.totalAmount}\n`;
    summary += `Payment Status: ${appointment.billing.paymentStatus}\n\n`;
    summary += `Record generated: ${new Date().toLocaleString()}\n`;

    return summary;
}

/**
 * Add medication to a service in appointment
 */
const addMedicationToService = async (req, res) => {
    try {
        const { appointmentId, appointmentCategoryId, categoryServiceId } = req.params;
        const {
            inventoryItemId,
            medicationName,
            quantity,
            unit,
            dosage,
            frequency,
            duration,
            instructions,
            batchNumber,
            expiryDate,
            unitCost
        } = req.body;

        const parsedAppointmentId = validateAppointmentId(appointmentId);
        const { staffId: currentStaffId } = getCurrentUserInfo(req);

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find category and service
        const category = appointment.appointmentCategories.find(
            cat => cat.appointmentCategoryId === parseInt(appointmentCategoryId)
        );
        if (!category) {
            return sendResponse(res, 404, false, "Appointment category not found");
        }

        const service = category.categoryServices.find(
            svc => svc.categoryServiceId === parseInt(categoryServiceId)
        );
        if (!service) {
            return sendResponse(res, 404, false, "Service not found in category");
        }

        // Check if inventory item exists and has sufficient quantity
        const Inventory = mongoose.model('Inventory');
        const inventoryItem = await Inventory.findOne({
            inventoryItemId: parseInt(inventoryItemId),
            clinicId: appointment.clinicId
        });

        if (!inventoryItem) {
            return sendResponse(res, 404, false, "Inventory item not found");
        }

        if (inventoryItem.quantity < quantity) {
            return sendResponse(res, 400, false, `Insufficient inventory. Available: ${inventoryItem.quantity}, Requested: ${quantity}`);
        }

        // Initialize medicationsUsed array if it doesn't exist
        if (!service.medicationsUsed) {
            service.medicationsUsed = [];
        }

        // Add medication to service
        const medicationData = {
            inventoryItemId: parseInt(inventoryItemId),
            medicationName: medicationName || inventoryItem.name,
            quantity: parseInt(quantity),
            unit: unit || inventoryItem.unit,
            dosage: dosage || '',
            frequency: frequency || '',
            duration: duration || '',
            instructions: instructions || '',
            batchNumber: batchNumber || inventoryItem.batchNumber,
            expiryDate: expiryDate || inventoryItem.expiryDate,
            unitCost: unitCost || inventoryItem.unitPrice,
            totalCost: (unitCost || inventoryItem.unitPrice) * quantity
        };

        service.medicationsUsed.push(medicationData);

        appointment.updatedBy = currentStaffId;
        await appointment.save();

        return sendResponse(res, 200, true, "Medication added to service successfully", {
            appointmentId: appointment.appointmentId,
            categoryId: appointmentCategoryId,
            serviceId: categoryServiceId,
            medication: medicationData
        });

    } catch (error) {
        console.error("Add medication to service error:", error);
        return sendResponse(res, 500, false, `Failed to add medication: ${error.message}`);
    }
};

/**
 * Remove medication from a service in appointment
 */
const removeMedicationFromService = async (req, res) => {
    try {
        const { appointmentId, appointmentCategoryId, categoryServiceId, medicationIndex } = req.params;

        const parsedAppointmentId = validateAppointmentId(appointmentId);
        const { staffId: currentStaffId } = getCurrentUserInfo(req);

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find category and service
        const category = appointment.appointmentCategories.find(
            cat => cat.appointmentCategoryId === parseInt(appointmentCategoryId)
        );
        if (!category) {
            return sendResponse(res, 404, false, "Appointment category not found");
        }

        const service = category.categoryServices.find(
            svc => svc.categoryServiceId === parseInt(categoryServiceId)
        );
        if (!service) {
            return sendResponse(res, 404, false, "Service not found in category");
        }

        if (!service.medicationsUsed || service.medicationsUsed.length === 0) {
            return sendResponse(res, 404, false, "No medications found for this service");
        }

        const medIndex = parseInt(medicationIndex);
        if (medIndex < 0 || medIndex >= service.medicationsUsed.length) {
            return sendResponse(res, 404, false, "Medication not found at specified index");
        }

        // Remove medication
        const removedMedication = service.medicationsUsed.splice(medIndex, 1)[0];

        appointment.updatedBy = currentStaffId;
        await appointment.save();

        return sendResponse(res, 200, true, "Medication removed from service successfully", {
            appointmentId: appointment.appointmentId,
            categoryId: appointmentCategoryId,
            serviceId: categoryServiceId,
            removedMedication
        });

    } catch (error) {
        console.error("Remove medication from service error:", error);
        return sendResponse(res, 500, false, `Failed to remove medication: ${error.message}`);
    }
};

/**
 * Clean up appointment data - remove performedBy fields from service level
 */
const cleanupAppointmentStaffData = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        let query = {};
        if (appointmentId) {
            const parsedAppointmentId = parseInt(appointmentId);
            if (isNaN(parsedAppointmentId)) {
                return sendResponse(res, 400, false, "Invalid appointment ID provided");
            }
            query = { appointmentId: parsedAppointmentId };
        }

        // Find appointments that have performedBy fields in services
        const appointments = await Appointment.find(query);
        let updatedCount = 0;

        for (const appointment of appointments) {
            let hasChanges = false;

            // Process each category
            appointment.appointmentCategories.forEach(category => {
                if (category.categoryServices) {
                    category.categoryServices.forEach(service => {
                        // Remove performedBy and performedByName from service level if they exist
                        if (service.performedBy !== undefined) {
                            delete service.performedBy;
                            hasChanges = true;
                        }
                        if (service.performedByName !== undefined) {
                            delete service.performedByName;
                            hasChanges = true;
                        }
                    });
                }
            });

            if (hasChanges) {
                appointment.updatedBy = req.user?.staffId || req.user?.userId || 1020;
                await appointment.save();
                updatedCount++;
            }
        }

        return sendResponse(res, 200, true, `Cleaned up staff data for ${updatedCount} appointments`, {
            totalProcessed: appointments.length,
            updatedCount,
            message: "Removed performedBy fields from service level - staff info now only at category level"
        });

    } catch (error) {
        console.error("Cleanup appointment staff data error:", error);
        return sendResponse(res, 500, false, `Failed to cleanup staff data: ${error.message}`);
    }
};

/**
 * Get appointment with properly structured staff data (category level only)
 */
const getAppointmentWithCleanStaffData = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        const parsedAppointmentId = validateAppointmentId(appointmentId);
        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Clean the appointment data to ensure no service-level staff info is returned
        const cleanedAppointment = appointment.toObject();

        cleanedAppointment.appointmentCategories.forEach(category => {
            if (category.categoryServices) {
                category.categoryServices.forEach(service => {
                    // Remove any service-level staff fields that might exist
                    delete service.performedBy;
                    delete service.performedByName;

                    // Add category-level staff info for reference
                    service.categoryStaffInfo = {
                        assignedStaff: category.staffAssigned,
                        assignedStaffName: category.staffAssignedName,
                        performedBy: category.performedBy,
                        performedByName: category.performedByName,
                        recordedBy: category.recordedBy,
                        recordedByName: category.recordedByName
                    };
                });
            }
        });

        return sendResponse(res, 200, true, "Appointment retrieved with clean staff data structure", cleanedAppointment);

    } catch (error) {
        console.error("Get appointment with clean staff data error:", error);
        return sendResponse(res, 500, false, `Failed to get appointment: ${error.message}`);
    }
};

/**
 * Complete entire appointment
 */
const completeAppointment = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const {
            finalNotes,
            recommendations,
            generateInvoice = false,
            generateReceipt = false,
            createMedicalRecord = false
        } = req.body;

        const parsedAppointmentId = validateAppointmentId(appointmentId);
        const { staffId: currentStaffId, staffName: currentStaffName } = getCurrentUserInfo(req);

        const appointment = await Appointment.findOne({ appointmentId: parsedAppointmentId });
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Check if appointment can be completed
        if (appointment.status === 'completed') {
            return sendResponse(res, 400, false, "Appointment is already completed");
        }

        // Update appointment status and completion details
        appointment.status = 'completed';
        appointment.completionStatus = 'completed';
        appointment.completedAt = new Date();
        appointment.completedBy = currentStaffId;
        appointment.completedByName = currentStaffName;

        if (finalNotes) appointment.generalNotes = finalNotes;
        if (recommendations) appointment.recommendations = recommendations;

        appointment.updatedBy = currentStaffId;
        await appointment.save();

        // Generate additional documents if requested
        const results = {
            appointment: appointment,
            invoice: null,
            receipt: null,
            medicalRecord: null
        };

        // Note: In a real implementation, you would call actual invoice/receipt/medical record generation services
        if (generateInvoice) {
            results.invoice = { message: "Invoice generation would be triggered here" };
        }

        if (generateReceipt) {
            results.receipt = { message: "Receipt generation would be triggered here" };
        }

        if (createMedicalRecord) {
            results.medicalRecord = { message: "Medical record creation would be triggered here" };
        }

        return sendResponse(res, 200, true, "Appointment completed successfully", results);
    } catch (error) {
        console.error("Complete appointment error:", error);
        return sendResponse(res, 400, false, error.message);
    }
};

// ========================================
// ENHANCED APPOINTMENT FEATURES (Phase 1)
// ========================================

/**
 * Check appointment availability
 */
const checkAppointmentAvailability = async (req, res) => {
    try {
        const { clinicId, staffId, appointmentDate, estimatedDuration, excludeAppointmentId } = req.body;

        const availability = await appointmentScheduler.checkAvailability({
            clinicId: parseInt(clinicId),
            staffId: staffId ? parseInt(staffId) : null,
            appointmentDate: new Date(appointmentDate),
            estimatedDuration: parseInt(estimatedDuration) || 30,
            excludeAppointmentId: excludeAppointmentId ? parseInt(excludeAppointmentId) : null
        });

        return sendResponse(res, 200, true, "Availability checked successfully", availability);
    } catch (error) {
        console.error("Error checking appointment availability:", error);
        return sendResponse(res, 500, false, `Failed to check availability: ${error.message}`);
    }
};

/**
 * Lock time slot temporarily
 */
const lockTimeSlot = async (req, res) => {
    try {
        const { clinicId, staffId, appointmentDate, sessionId } = req.body;

        const lockKey = appointmentScheduler.lockTimeSlot({
            clinicId: parseInt(clinicId),
            staffId: staffId ? parseInt(staffId) : null,
            appointmentDate: new Date(appointmentDate),
            sessionId: sessionId || req.sessionID
        });

        return sendResponse(res, 200, true, "Time slot locked successfully", { lockKey });
    } catch (error) {
        console.error("Error locking time slot:", error);
        return sendResponse(res, 500, false, `Failed to lock time slot: ${error.message}`);
    }
};

/**
 * Release locked time slot
 */
const releaseTimeSlot = async (req, res) => {
    try {
        const { lockKey, sessionId } = req.body;

        const released = appointmentScheduler.releaseTimeSlot(lockKey, sessionId || req.sessionID);

        return sendResponse(res, 200, true, released ? "Time slot released successfully" : "Failed to release time slot", { released });
    } catch (error) {
        console.error("Error releasing time slot:", error);
        return sendResponse(res, 500, false, `Failed to release time slot: ${error.message}`);
    }
};

/**
 * Get daily schedule
 */
const getDailySchedule = async (req, res) => {
    try {
        const { clinicId, staffId, date } = req.query;

        const schedule = await appointmentScheduler.getDailySchedule({
            clinicId: parseInt(clinicId),
            staffId: staffId ? parseInt(staffId) : null,
            date: new Date(date)
        });

        return sendResponse(res, 200, true, "Daily schedule retrieved successfully", schedule);
    } catch (error) {
        console.error("Error getting daily schedule:", error);
        return sendResponse(res, 500, false, `Failed to get daily schedule: ${error.message}`);
    }
};

/**
 * Optimize schedule
 */
const optimizeSchedule = async (req, res) => {
    try {
        const { clinicId, staffId, date } = req.query;

        const optimization = await appointmentScheduler.optimizeSchedule({
            clinicId: parseInt(clinicId),
            staffId: staffId ? parseInt(staffId) : null,
            date: new Date(date)
        });

        return sendResponse(res, 200, true, "Schedule optimization completed", optimization);
    } catch (error) {
        console.error("Error optimizing schedule:", error);
        return sendResponse(res, 500, false, `Failed to optimize schedule: ${error.message}`);
    }
};

/**
 * Add to waiting list
 */
const addToWaitingList = async (req, res) => {
    try {
        const waitingListData = req.body;
        const waitingListEntry = await waitingListService.addToWaitingList(waitingListData);

        return sendResponse(res, 201, true, "Added to waiting list successfully", waitingListEntry);
    } catch (error) {
        console.error("Error adding to waiting list:", error);
        return sendResponse(res, 500, false, `Failed to add to waiting list: ${error.message}`);
    }
};

/**
 * Get waiting list
 */
const getWaitingList = async (req, res) => {
    try {
        const { clinicId, status } = req.query;
        const filters = {};
        if (status) filters.status = status;

        const waitingList = await waitingListService.getWaitingList(parseInt(clinicId), filters);

        return sendResponse(res, 200, true, "Waiting list retrieved successfully", waitingList);
    } catch (error) {
        console.error("Error getting waiting list:", error);
        return sendResponse(res, 500, false, `Failed to get waiting list: ${error.message}`);
    }
};

/**
 * Confirm appointment from waiting list
 */
const confirmWaitingListAppointment = async (req, res) => {
    try {
        const { waitingListId } = req.params;
        const appointmentData = req.body;

        const confirmed = await waitingListService.confirmAppointment(parseInt(waitingListId), appointmentData);

        return sendResponse(res, 200, true, "Appointment confirmed from waiting list", { confirmed });
    } catch (error) {
        console.error("Error confirming waiting list appointment:", error);
        return sendResponse(res, 500, false, `Failed to confirm appointment: ${error.message}`);
    }
};

/**
 * Cancel waiting list entry
 */
const cancelWaitingListEntry = async (req, res) => {
    try {
        const { waitingListId } = req.params;
        const { reason } = req.body;

        const cancelled = await waitingListService.cancelWaitingListEntry(parseInt(waitingListId), reason);

        return sendResponse(res, 200, true, "Waiting list entry cancelled", cancelled);
    } catch (error) {
        console.error("Error cancelling waiting list entry:", error);
        return sendResponse(res, 500, false, `Failed to cancel waiting list entry: ${error.message}`);
    }
};

/**
 * Create recurring appointment (temporarily disabled)
 */
const createRecurringAppointment = async (req, res) => {
    try {
        return sendResponse(res, 501, false, "Recurring appointments feature temporarily disabled");
    } catch (error) {
        console.error("Error creating recurring appointment:", error);
        return sendResponse(res, 500, false, `Failed to create recurring appointment: ${error.message}`);
    }
};

/**
 * Get recurring appointments (temporarily disabled)
 */
const getRecurringAppointments = async (req, res) => {
    try {
        return sendResponse(res, 501, false, "Recurring appointments feature temporarily disabled");
    } catch (error) {
        console.error("Error getting recurring appointments:", error);
        return sendResponse(res, 500, false, `Failed to get recurring appointments: ${error.message}`);
    }
};

/**
 * Update recurring appointment
 */
const updateRecurringAppointment = async (req, res) => {
    try {
        const { recurringId } = req.params;
        const updateData = req.body;

        const RecurringAppointment = mongoose.model('RecurringAppointment');
        const updated = await RecurringAppointment.findOneAndUpdate(
            { recurringId: parseInt(recurringId) },
            updateData,
            { new: true }
        );

        if (!updated) {
            return sendResponse(res, 404, false, "Recurring appointment not found");
        }

        return sendResponse(res, 200, true, "Recurring appointment updated successfully", updated);
    } catch (error) {
        console.error("Error updating recurring appointment:", error);
        return sendResponse(res, 500, false, `Failed to update recurring appointment: ${error.message}`);
    }
};

/**
 * Pause recurring appointment
 */
const pauseRecurringAppointment = async (req, res) => {
    try {
        const { recurringId } = req.params;

        const RecurringAppointment = mongoose.model('RecurringAppointment');
        const updated = await RecurringAppointment.findOneAndUpdate(
            { recurringId: parseInt(recurringId) },
            { status: 'paused' },
            { new: true }
        );

        if (!updated) {
            return sendResponse(res, 404, false, "Recurring appointment not found");
        }

        return sendResponse(res, 200, true, "Recurring appointment paused successfully", updated);
    } catch (error) {
        console.error("Error pausing recurring appointment:", error);
        return sendResponse(res, 500, false, `Failed to pause recurring appointment: ${error.message}`);
    }
};

/**
 * Resume recurring appointment
 */
const resumeRecurringAppointment = async (req, res) => {
    try {
        const { recurringId } = req.params;

        const RecurringAppointment = mongoose.model('RecurringAppointment');
        const updated = await RecurringAppointment.findOneAndUpdate(
            { recurringId: parseInt(recurringId) },
            { status: 'active' },
            { new: true }
        );

        if (!updated) {
            return sendResponse(res, 404, false, "Recurring appointment not found");
        }

        return sendResponse(res, 200, true, "Recurring appointment resumed successfully", updated);
    } catch (error) {
        console.error("Error resuming recurring appointment:", error);
        return sendResponse(res, 500, false, `Failed to resume recurring appointment: ${error.message}`);
    }
};

/**
 * Cancel recurring appointment
 */
const cancelRecurringAppointment = async (req, res) => {
    try {
        const { recurringId } = req.params;

        const RecurringAppointment = mongoose.model('RecurringAppointment');
        const updated = await RecurringAppointment.findOneAndUpdate(
            { recurringId: parseInt(recurringId) },
            { status: 'cancelled' },
            { new: true }
        );

        if (!updated) {
            return sendResponse(res, 404, false, "Recurring appointment not found");
        }

        return sendResponse(res, 200, true, "Recurring appointment cancelled successfully", updated);
    } catch (error) {
        console.error("Error cancelling recurring appointment:", error);
        return sendResponse(res, 500, false, `Failed to cancel recurring appointment: ${error.message}`);
    }
};

export {
    createAppointment,
    updateAppointment,
    getAllNewAppointments as getAllAppointments,
    getNewAppointmentById as getAppointmentById,
    getAppointmentCategories,
    getCategoryServices,
    addServiceToAppointment,
    completeService,
    updateAppointmentNotes,
    assignCategoriesToAppointment,
    deleteAppointmentCategory,
    deleteServiceFromAppointment,
    updateAppointmentCategory,
    updateServiceInAppointment,
    assignStaffToAppointment,
    updateAppointmentStatus,
    addServiceToAppointmentWorkflow,
    addTaskToAppointment,
    getCurrentAppointments,
    createFollowUpAppointment,
    scheduleFollowUpReminder,
    getAppointmentChain,
    getOverdueFollowUps,
    getUpcomingFollowUps,
    // getAppointmentTaskStatus, // Deprecated - use appointment status instead
    completeAppointmentCategory,
    generateAppointmentSummary,
    generateAIAppointmentNotes,
    generateMedicalRecord,
    addMedicationToService,
    removeMedicationFromService,
    cleanupAppointmentStaffData,
    getAppointmentWithCleanStaffData,
    migrateStaffAssignments,
    completeAppointment,
    // Enhanced appointment features
    checkAppointmentAvailability,
    lockTimeSlot,
    releaseTimeSlot,
    getDailySchedule,
    optimizeSchedule,
    addToWaitingList,
    getWaitingList,
    confirmWaitingListAppointment,
    cancelWaitingListEntry,
    createRecurringAppointment,
    getRecurringAppointments,
    updateRecurringAppointment,
    pauseRecurringAppointment,
    resumeRecurringAppointment,
    cancelRecurringAppointment
};
