import Clinic from "../models/clinic.model.js";
import Staff from "../models/staff.model.js";
import Role from "../models/role.model.js";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import mongoose from "mongoose";
import { JWT_SECRET, JWT_EXPIRES_IN } from "../config/env.js";
import { sendResponse, paginateResults } from '../utils/responseHandler.js';
import User from "../models/user.model.js";

// Multi-Clinic Management Functions

/**
 * Get all clinics for the current user (owner/staff)
 */
export const getMyClinicsList = async (req, res) => {
    try {
        const { userId } = req.user;

        // Find staff record for this user
        const staffRecord = await Staff.findOne({ userId }).lean();
        if (!staffRecord) {
            return sendResponse(res, 404, false, "Staff record not found");
        }

        // Get primary clinic and additional clinics
        const clinicIds = [staffRecord.clinicId, ...staffRecord.additionalClinics];
        const uniqueClinicIds = [...new Set(clinicIds)];

        const clinics = await Clinic.find({
            clinicId: { $in: uniqueClinicIds },
            status: 1
        })
        .select('clinicId clinicName address city state phoneNumber email subscription.plan')
        .lean();

        // Add role information for each clinic
        const clinicsWithRoles = clinics.map(clinic => ({
            ...clinic,
            role: clinic.clinicId === staffRecord.clinicId ? 'primary' : 'additional',
            isOwner: clinic.owner === staffRecord.staffId,
            isCurrentClinic: clinic.clinicId === staffRecord.currentClinicId
        }));

        return sendResponse(res, 200, true, "Clinics retrieved successfully", {
            clinics: clinicsWithRoles,
            currentClinicId: staffRecord.currentClinicId,
            totalClinics: clinicsWithRoles.length
        });
    } catch (error) {
        console.error("Error getting user clinics:", error);
        return sendResponse(res, 500, false, "Error retrieving clinics: " + error.message);
    }
};

/**
 * Register a new clinic (for clinic owners)
 */
export const registerNewClinic = async (req, res) => {
    try {
        const { userId } = req.user;
        const {
            clinicName,
            phoneNumber,
            email,
            address,
            city,
            state,
            zipCode,
            country = 'USA',
            operatingHours,
            settings = {},
            services = []
        } = req.body;

        // Verify user is a clinic owner
        const staffRecord = await Staff.findOne({ userId }).lean();
        if (!staffRecord || !staffRecord.isClinicOwner) {
            return sendResponse(res, 403, false, "Only clinic owners can register new clinics");
        }

        // Check if email is already used
        const existingClinic = await Clinic.findOne({ email }).lean();
        if (existingClinic) {
            return sendResponse(res, 409, false, "Email already registered for another clinic");
        }

        // Create new clinic
        const newClinic = await Clinic.create({
            clinicName,
            owner: staffRecord.staffId,
            phoneNumber,
            email,
            address,
            city,
            state,
            zipCode,
            country,
            operatingHours,
            settings: {
                ...settings,
                timezone: settings.timezone || 'America/New_York',
                currency: settings.currency || 'USD',
                appointmentDuration: settings.appointmentDuration || 30,
                allowWalkIns: settings.allowWalkIns !== undefined ? settings.allowWalkIns : true
            },
            services,
            status: 1
        });

        // Add this clinic to staff's additional clinics
        await Staff.updateOne(
            { staffId: staffRecord.staffId },
            { $addToSet: { additionalClinics: newClinic.clinicId } }
        );

        return sendResponse(res, 201, true, "Clinic registered successfully", {
            clinic: {
                clinicId: newClinic.clinicId,
                clinicName: newClinic.clinicName,
                address: newClinic.address,
                city: newClinic.city,
                state: newClinic.state,
                phoneNumber: newClinic.phoneNumber,
                email: newClinic.email
            }
        });
    } catch (error) {
        console.error("Error registering clinic:", error);
        return sendResponse(res, 500, false, "Error registering clinic: " + error.message);
    }
};

/**
 * Switch current clinic context
 */
export const switchCurrentClinic = async (req, res) => {
    try {
        const { userId } = req.user;
        const { clinicId } = req.body;

        if (!clinicId) {
            return sendResponse(res, 400, false, "Clinic ID is required");
        }

        // Find staff record
        const staffRecord = await Staff.findOne({ userId });
        if (!staffRecord) {
            return sendResponse(res, 404, false, "Staff record not found");
        }

        // Verify staff has access to this clinic
        const hasAccess = staffRecord.clinicId === clinicId ||
                         staffRecord.additionalClinics.includes(clinicId);

        if (!hasAccess) {
            return sendResponse(res, 403, false, "Access denied to this clinic");
        }

        // Verify clinic exists and is active
        const clinic = await Clinic.findOne({ clinicId, status: 1 }).lean();
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found or inactive");
        }

        // Update current clinic
        await Staff.updateOne(
            { staffId: staffRecord.staffId },
            { currentClinicId: clinicId }
        );

        // Generate new token with clinic context
        const tokenPayload = {
            userId: staffRecord.userId,
            staffId: staffRecord.staffId,
            clinicId: clinicId,
            isClinicOwner: staffRecord.isClinicOwner
        };

        const newToken = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

        return sendResponse(res, 200, true, "Clinic switched successfully", {
            token: newToken,
            currentClinic: {
                clinicId: clinic.clinicId,
                clinicName: clinic.clinicName,
                address: clinic.address,
                city: clinic.city,
                state: clinic.state
            }
        });
    } catch (error) {
        console.error("Error switching clinic:", error);
        return sendResponse(res, 500, false, "Error switching clinic: " + error.message);
    }
};

/**
 * Assign staff to clinic
 */
export const assignStaffToClinic = async (req, res) => {
    try {
        const { userId } = req.user;
        const { clinicId } = req.params;
        const { staffId, roleId, permissions = [] } = req.body;

        // Verify current user is owner/manager of the clinic
        const currentStaff = await Staff.findOne({ userId }).lean();
        if (!currentStaff) {
            return sendResponse(res, 404, false, "Staff record not found");
        }

        const clinic = await Clinic.findOne({ clinicId: parseInt(clinicId) }).lean();
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Check if user has permission to assign staff
        const canAssign = clinic.owner === currentStaff.staffId ||
                         clinic.managerId === currentStaff.staffId;

        if (!canAssign) {
            return sendResponse(res, 403, false, "Only clinic owners or managers can assign staff");
        }

        // Find the staff to be assigned
        const targetStaff = await Staff.findOne({ staffId: parseInt(staffId) });
        if (!targetStaff) {
            return sendResponse(res, 404, false, "Target staff not found");
        }

        // Add clinic to staff's additional clinics if not already present
        if (!targetStaff.additionalClinics.includes(parseInt(clinicId))) {
            targetStaff.additionalClinics.push(parseInt(clinicId));
        }

        // Update role and permissions if provided
        if (roleId) {
            targetStaff.roleId = roleId;
        }
        if (permissions.length > 0) {
            targetStaff.specialPermissions = permissions;
        }

        await targetStaff.save();

        return sendResponse(res, 200, true, "Staff assigned to clinic successfully", {
            staffId: targetStaff.staffId,
            clinicId: parseInt(clinicId),
            role: roleId,
            permissions
        });
    } catch (error) {
        console.error("Error assigning staff to clinic:", error);
        return sendResponse(res, 500, false, "Error assigning staff: " + error.message);
    }
};

/**
 * Get clinic staff with roles and permissions
 */
export const getClinicStaff = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { page = 1, limit = 20 } = req.query;

        // Find all staff assigned to this clinic
        const staff = await Staff.find({
            $or: [
                { clinicId: parseInt(clinicId) },
                { additionalClinics: parseInt(clinicId) }
            ],
            status: 1
        })
        .populate('userId', 'firstName lastName email phoneNumber')
        .populate('roleId', 'roleName permissions')
        .select('staffId userId roleId jobTitle specializations isClinicOwner clinicId additionalClinics specialPermissions')
        .lean();

        // Get clinic info
        const clinic = await Clinic.findOne({ clinicId: parseInt(clinicId) })
            .select('clinicName owner managerId')
            .lean();

        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Format staff data with role information
        const formattedStaff = staff.map(member => ({
            staffId: member.staffId,
            name: `${member.userId.firstName} ${member.userId.lastName}`,
            email: member.userId.email,
            phoneNumber: member.userId.phoneNumber,
            jobTitle: member.jobTitle,
            role: member.roleId?.roleName || 'Unknown',
            permissions: member.roleId?.permissions || [],
            specialPermissions: member.specialPermissions || [],
            specializations: member.specializations || [],
            isOwner: clinic.owner === member.staffId,
            isManager: clinic.managerId === member.staffId,
            isPrimaryClinic: member.clinicId === parseInt(clinicId),
            isActive: member.status === 1
        }));

        const paginatedResults = paginateResults(formattedStaff, parseInt(page), parseInt(limit));

        return sendResponse(res, 200, true, "Clinic staff retrieved successfully", {
            ...paginatedResults,
            clinicInfo: {
                clinicId: clinic.clinicId,
                clinicName: clinic.clinicName,
                totalStaff: formattedStaff.length
            }
        });
    } catch (error) {
        console.error("Error getting clinic staff:", error);
        return sendResponse(res, 500, false, "Error retrieving clinic staff: " + error.message);
    }
};

/**
 * Update clinic settings
 */
export const updateClinicSettings = async (req, res) => {
    try {
        const { userId } = req.user;
        const { clinicId } = req.params;
        const { settings, operatingHours, services } = req.body;

        // Verify user has permission to update clinic
        const staff = await Staff.findOne({ userId }).lean();
        if (!staff) {
            return sendResponse(res, 404, false, "Staff record not found");
        }

        const clinic = await Clinic.findOne({ clinicId: parseInt(clinicId) });
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Check permissions
        const canUpdate = clinic.owner === staff.staffId || clinic.managerId === staff.staffId;
        if (!canUpdate) {
            return sendResponse(res, 403, false, "Only clinic owners or managers can update settings");
        }

        // Update clinic settings
        if (settings) {
            clinic.settings = { ...clinic.settings, ...settings };
        }
        if (operatingHours) {
            clinic.operatingHours = { ...clinic.operatingHours, ...operatingHours };
        }
        if (services) {
            clinic.services = services;
        }

        await clinic.save();

        return sendResponse(res, 200, true, "Clinic settings updated successfully", {
            clinicId: clinic.clinicId,
            settings: clinic.settings,
            operatingHours: clinic.operatingHours,
            services: clinic.services
        });
    } catch (error) {
        console.error("Error updating clinic settings:", error);
        return sendResponse(res, 500, false, "Error updating clinic settings: " + error.message);
    }
};

export const createClinic = async (req, res) => {
    try {
        const {
            name,
            ownerId,
            phoneNumber,
            email,
            address,
            location,
            operatingHours
        } = req.body;

        const owner = await Staff.findOne({ staffId: parseInt(ownerId) }).lean();
        if (!owner) {
            return sendResponse(res, 404, false, "Owner not found");
        }

        if (!owner.isClinicOwner) {
            return sendResponse(res, 403, false, "User is not authorized to own a clinic");
        }

        const clinic = await Clinic.create({
            clinicName: name,
            ownerId: owner.staffId,
            phoneNumber,
            email,
            address,
            location: location || { type: 'Point', coordinates: [0, 0] },
            operatingHours,
            status: 1
        });

        return sendResponse(res, 201, true, "Clinic created successfully", clinic);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const searchNearbyClinic = async (req, res) => {
    try {
        const {
            latitude,
            longitude,
            radius = 5000, // Default 5km
            page = 1,
            limit = 10
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const query = {
            location: {
                $near: {
                    $geometry: {
                        type: 'Point',
                        coordinates: [parseFloat(longitude), parseFloat(latitude)]
                    },
                    $maxDistance: parseInt(radius)
                }
            },
            status: 1
        };

        const [clinics, totalCount] = await Promise.all([
            Clinic.find(query)
                .populate('ownerId', 'firstName middleName lastName email -_id')
                .select('-__v')
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Clinic.countDocuments(query)
        ]);

        return sendResponse(res, 200, true, "Nearby clinics found successfully", {
            data: clinics,
            pagination: {
                totalCount: totalCount,
                page: parseInt(page),
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit),
                totalPages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getAllClinics = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "name",
            sortOrder = "asc",
            search,
            status,
            ownerId
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (status !== undefined) query.status = parseInt(status);

        // Filter by owner if ownerId is provided
        if (ownerId) {
            query.owner = parseInt(ownerId);
        }

        if (search) {
            query.$or = [
                { clinicName: { $regex: search, $options: "i" } },
                { email: { $regex: search, $options: "i" } },
                { phoneNumber: { $regex: search, $options: "i" } },
                { address: { $regex: search, $options: "i" } }
            ];
        }

        const [clinics, totalCount] = await Promise.all([
            Clinic.find(query)
                .select('-__v')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            Clinic.countDocuments(query)
        ]);

        // Populate owner information for each clinic
        const clinicsWithOwners = await Promise.all(
            clinics.map(async (clinic) => {
                if (clinic.owner) {
                    const owner = await Staff.findOne({ staffId: clinic.owner })
                        .select('staffId firstName lastName email phoneNumber jobTitle')
                        .lean();
                    return { ...clinic, ownerInfo: owner };
                }
                return clinic;
            })
        );

        return sendResponse(
            res,
            200,
            true,
            "Clinics retrieved successfully",
            paginateResults(clinicsWithOwners, totalCount, page, limit)
        );
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const getClinicById = async (req, res) => {
    try {
        const { clinicId } = req.params;

        // Use numeric clinicId only
        const query = { clinicId: parseInt(clinicId) };

        const clinic = await Clinic.findOne(query)
            .lean();

        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        return sendResponse(res, 200, true, "Clinic found successfully", clinic);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

export const updateClinic = async (req, res) => {
    try {
        const { clinicId } = req.params;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        const clinic = await Clinic.findOneAndUpdate(
            query,
            { $set: req.body },
            { new: true, runValidators: true }
        );

        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        return sendResponse(res, 200, true, "Clinic updated successfully", clinic);
    } catch (error) {
        return sendResponse(res, 400, false, error.message);
    }
};

/**
 * Update clinic profile (comprehensive clinic management)
 */
export const updateClinicProfile = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const updateData = req.body;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        // Verify clinic exists
        const existingClinic = await Clinic.findOne(query);
        if (!existingClinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // If owner is being updated, validate the staff member exists
        if (updateData.owner) {
            const staff = await Staff.findOne({ staffId: updateData.owner });
            if (!staff) {
                return sendResponse(res, 404, false, "Invalid owner specified");
            }
        }

        // Update the clinic
        const updatedClinic = await Clinic.findOneAndUpdate(
            query,
            { $set: updateData },
            { new: true, runValidators: true }
        ).lean();

        // Get owner and staff information
        const [owner, staffMembers] = await Promise.all([
            updatedClinic.owner ? Staff.findOne({ staffId: updatedClinic.owner })
                .select('staffId firstName lastName email phoneNumber jobTitle')
                .lean() : null,
            Staff.find({ clinicId: updatedClinic.clinicId })
                .select('staffId firstName lastName email phoneNumber jobTitle roleId')
                .lean()
        ]);

        // Get roles for staff members
        const roleIds = [...new Set(staffMembers.map(s => s.roleId))];
        const roles = await Role.find({ roleId: { $in: roleIds } }).lean();
        const roleMap = roles.reduce((map, role) => {
            map[role.roleId] = role;
            return map;
        }, {});

        // Populate staff with role information
        const populatedStaff = staffMembers.map(staff => ({
            ...staff,
            role: roleMap[staff.roleId] ? {
                roleId: roleMap[staff.roleId].roleId,
                roleName: roleMap[staff.roleId].name || roleMap[staff.roleId].roleName,
                description: roleMap[staff.roleId].description
            } : null
        }));

        // Prepare response data
        const responseData = {
            ...updatedClinic,
            owner: owner,
            staff: populatedStaff,
            staffCount: populatedStaff.length
        };

        return sendResponse(res, 200, true, "Clinic profile updated successfully", responseData);
    } catch (error) {
        console.error("Update clinic profile error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

export const deleteClinic = async (req, res) => {
    try {
        const { clinicId } = req.params;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        const clinic = await Clinic.findOneAndDelete(query);

        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        return sendResponse(res, 200, true, "Clinic deleted successfully");
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Switch the current clinic for a user
 * This allows staff members who work at multiple clinics to switch between them
 */
/**
 * Admin endpoint to register a clinic owner with default password
 * This creates both the clinic and the clinic owner staff member
 */
export const registerClinicOwner = async (req, res) => {
    const {
        // Owner details
        firstName,
        middleName,
        lastName,
        email,
        phoneNumber,
        address,
        dob,
        jobTitle,
        employmentDate,
        salary,
        emergencyContact,
        schedule,
        // Clinic details
        clinicName,
        clinicPhoneNumber,
        clinicEmail,
        clinicAddress,
        location,
        operatingHours
    } = req.body;

    let session = null;
    try {
        session = await mongoose.startSession();
        session.startTransaction();

        // Check if staff already exists
        const existingStaff = await Staff.findOne({ email }).session(session);
        if (existingStaff) {
            return sendResponse(res, 409, false, "Staff with this email already exists");
        }

        // Get the clinic admin role (roleId: 1002)
        const clinicOwnerRole = await Role.findOne({ roleId: 1002 }).session(session);
        if (!clinicOwnerRole) {
            return sendResponse(res, 404, false, "Clinic admin role not found");
        }

        // Hash the default password
        const defaultPassword = 'pass123';
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(defaultPassword, salt);

        // Create the clinic first
        const [newClinic] = await Clinic.create([{
            clinicName: clinicName,
            phoneNumber: clinicPhoneNumber || phoneNumber,
            email: clinicEmail || email,
            address: clinicAddress || address,
            location: location || { type: 'Point', coordinates: [0, 0] },
            operatingHours: operatingHours || {
                monday: { open: '08:00', close: '18:00' },
                tuesday: { open: '08:00', close: '18:00' },
                wednesday: { open: '08:00', close: '18:00' },
                thursday: { open: '08:00', close: '18:00' },
                friday: { open: '08:00', close: '18:00' },
                saturday: { open: '08:00', close: '16:00' },
                sunday: { closed: true }
            },
            status: 1
        }], { session });

        // Create the clinic owner staff member
        const staffData = {
            firstName,
            middleName: middleName || '',
            lastName,
            email,
            password: hashedPassword,
            phoneNumber,
            address: address || '',
            dob: dob ? new Date(dob) : null,
            clinicId: newClinic.clinicId,
            primaryClinicId: newClinic.clinicId,
            isClinicOwner: true,
            roleId: clinicOwnerRole.roleId,
            permissions: clinicOwnerRole.permissions,
            jobTitle: jobTitle || 'Clinic Owner',
            employmentDate: employmentDate ? new Date(employmentDate) : new Date(),
            salary: salary || 0,
            emergencyContact: emergencyContact || {
                name: '',
                relationship: '',
                phoneNumber: ''
            },
            schedule: schedule || {
                workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                workHours: { start: '08:00', end: '18:00' }
            },
            status: 1,
            lastLogin: null,
            loginCount: 0,
            clinicActivity: [{
                clinicId: newClinic.clinicId,
                lastActive: new Date(),
                activityCount: 1
            }],
            currentClinicId: newClinic.clinicId
        };

        const [newStaff] = await Staff.create([staffData], { session });

        // Update the clinic with the owner information
        await Clinic.findOneAndUpdate(
            { clinicId: newClinic.clinicId },
            { owner: newStaff.staffId },
            { session }
        );

        // Prepare response data
        const staffWithoutPassword = { ...newStaff._doc };
        delete staffWithoutPassword.password;

        const responseData = {
            clinic: {
                clinicId: newClinic.clinicId,
                clinicName: newClinic.clinicName,
                phoneNumber: newClinic.phoneNumber,
                email: newClinic.email,
                address: newClinic.address,
                status: newClinic.status
            },
            owner: staffWithoutPassword,
            credentials: {
                email: email,
                password: defaultPassword,
                message: "Default password is 'pass123'. Owner should change it on first login."
            }
        };

        await session.commitTransaction();
        return sendResponse(res, 201, true, "Clinic and owner registered successfully", responseData);
    } catch (error) {
        if (session?.inTransaction()) {
            await session.abortTransaction();
        }
        console.error("Register clinic owner error:", error);
        return sendResponse(res, 500, false, error.message);
    } finally {
        if (session) {
            session.endSession();
        }
    }
};

export const switchClinic = async (req, res) => {
    try {
        const { clinicId } = req.body;
        const userId = req.user.userId || 0;

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = { status: 1 };
        if (!isNaN(clinicId)) {
            query.clinicId = parseInt(clinicId);
        } else {
            query._id = clinicId;
        }

        // Verify the clinic exists and is active
        const clinic = await Clinic.findOne(query).lean();
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found or inactive");
        }

        // Check if we have a staffId in the request
        if (req.user.staffId) {
            // This is a staff member
            const staff = await Staff.findOne({ staffId: parseInt(req.user.staffId) }).lean();
            if (!staff) {
                return sendResponse(res, 404, false, "Staff not found");
            }

            // Update the staff's current clinic
            await Staff.findOneAndUpdate({ staffId: parseInt(req.user.staffId) }, { currentClinicId: clinicId });
        } else {
            // This is a regular user
            const user = await Staff.findOne({ staffId: parseInt(req.user.staffId) }).lean();
            if (!user) {
                return sendResponse(res, 404, false, "User not found");
            }

            // Update the user's current clinic
            await Staff.findOneAndUpdate({ staffId: parseInt(req.user.staffId) }, { currentClinicId: clinicId });
        }

        // Authorization check will be reimplemented later
        // For now, all authenticated users can access any clinic

        // Current clinic already updated above

        // Generate a new token with the clinic info
        let tokenPayload = {
            userId: userId,
            currentClinicId: clinicId
        };

        // Include staffId in the token if it exists
        if (req.user.staffId) {
            tokenPayload.staffId = req.user.staffId;
        }

        const token = jwt.sign(
            tokenPayload,
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        // Get the full clinic details to return
        const clinicDetails = await Clinic.findOne(query)
            .select('clinicId clinicName address phoneNumber email status')
            .lean();

        return sendResponse(res, 200, true, "Clinic switched successfully", {
            token,
            currentClinic: clinicDetails
        });
    } catch (error) {
        console.error("Error switching clinic:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Admin endpoint to assign/attach a clinic to an existing owner
 * This updates the clinic's owner field
 */
export const assignClinicOwner = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { ownerId } = req.body;

        // Validate input
        if (!ownerId) {
            return sendResponse(res, 400, false, "Owner ID is required");
        }

        // Use numeric clinicId only
        const clinicQuery = { clinicId: parseInt(clinicId) };

        // Verify clinic exists
        const clinic = await Clinic.findOne(clinicQuery);
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Verify staff member exists and can be an owner
        const staff = await Staff.findOne({ staffId: parseInt(ownerId) });
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found");
        }

        // Update clinic with new owner
        const updatedClinic = await Clinic.findOneAndUpdate(
            clinicQuery,
            {
                owner: parseInt(ownerId),
                updatedAt: new Date()
            },
            { new: true, runValidators: true }
        ).lean();

        // Update staff member to be clinic owner if not already
        await Staff.findOneAndUpdate(
            { staffId: parseInt(ownerId) },
            {
                isClinicOwner: true,
                clinicId: updatedClinic.clinicId,
                primaryClinicId: updatedClinic.clinicId,
                updatedAt: new Date()
            }
        );

        // Get updated owner information
        const ownerInfo = await Staff.findOne({ staffId: parseInt(ownerId) })
            .select('staffId firstName lastName email phoneNumber jobTitle isClinicOwner')
            .lean();

        const responseData = {
            clinic: {
                clinicId: updatedClinic.clinicId,
                clinicName: updatedClinic.clinicName,
                owner: updatedClinic.owner
            },
            owner: ownerInfo
        };

        return sendResponse(res, 200, true, "Clinic owner assigned successfully", responseData);
    } catch (error) {
        console.error("Assign clinic owner error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};

/**
 * Clinic owner endpoint to assign a clinic manager
 * This updates the clinic's manager field
 */
export const assignClinicManager = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { managerId } = req.body;

        // Validate input
        if (!managerId) {
            return sendResponse(res, 400, false, "Manager ID is required");
        }

        // Use numeric clinicId only
        const clinicQuery = { clinicId: parseInt(clinicId) };

        // Verify clinic exists
        const clinic = await Clinic.findOne(clinicQuery);
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Verify staff member exists and belongs to this clinic
        const staff = await Staff.findOne({
            staffId: parseInt(managerId),
            clinicId: parseInt(clinicId)
        });
        if (!staff) {
            return sendResponse(res, 404, false, "Staff member not found in this clinic");
        }

        // Update clinic with new manager
        const updatedClinic = await Clinic.findOneAndUpdate(
            clinicQuery,
            {
                managerId: parseInt(managerId),
                updatedAt: new Date()
            },
            { new: true, runValidators: true }
        ).lean();

        // Update staff member role to manager if needed
        await Staff.findOneAndUpdate(
            { staffId: parseInt(managerId) },
            {
                isManager: true,
                updatedAt: new Date()
            }
        );

        // Get updated manager information
        const managerInfo = await Staff.findOne({ staffId: parseInt(managerId) })
            .select('staffId firstName lastName email phoneNumber jobTitle isManager')
            .lean();

        const responseData = {
            clinic: {
                clinicId: updatedClinic.clinicId,
                clinicName: updatedClinic.clinicName,
                managerId: updatedClinic.managerId
            },
            manager: managerInfo
        };

        return sendResponse(res, 200, true, "Clinic manager assigned successfully", responseData);
    } catch (error) {
        console.error("Assign clinic manager error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};



/**
 * Admin endpoint to remove staff from a clinic
 * This sets their clinicId to null or moves them to another clinic
 */
export const removeStaffFromClinic = async (req, res) => {
    try {
        const { clinicId } = req.params;
        const { staffIds, newClinicId } = req.body;

        // Validate input
        if (!staffIds || !Array.isArray(staffIds) || staffIds.length === 0) {
            return sendResponse(res, 400, false, "Staff IDs array is required");
        }

        // Check if clinicId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let clinicQuery = {};
        if (!isNaN(clinicId)) {
            clinicQuery.clinicId = parseInt(clinicId);
        } else {
            clinicQuery._id = clinicId;
        }

        // Verify clinic exists
        const clinic = await Clinic.findOne(clinicQuery);
        if (!clinic) {
            return sendResponse(res, 404, false, "Clinic not found");
        }

        // Verify new clinic exists if provided
        let newClinic = null;
        if (newClinicId) {
            let newClinicQuery = {};
            if (!isNaN(newClinicId)) {
                newClinicQuery.clinicId = parseInt(newClinicId);
            } else {
                newClinicQuery._id = newClinicId;
            }

            newClinic = await Clinic.findOne(newClinicQuery);
            if (!newClinic) {
                return sendResponse(res, 404, false, "New clinic not found");
            }
        }

        // Verify all staff members exist and belong to the clinic
        const staffMembers = await Staff.find({
            staffId: { $in: staffIds.map(id => parseInt(id)) },
            clinicId: clinic.clinicId
        });

        if (staffMembers.length !== staffIds.length) {
            return sendResponse(res, 404, false, "One or more staff members not found in this clinic");
        }

        // Check if any staff member is the clinic owner
        const clinicOwners = staffMembers.filter(staff =>
            staff.isClinicOwner && clinic.owner === staff.staffId
        );

        if (clinicOwners.length > 0) {
            return sendResponse(res, 400, false, "Cannot remove clinic owner. Please assign a new owner first.");
        }

        // Update staff members
        const updateData = {
            clinicId: newClinic ? newClinic.clinicId : null,
            currentClinicId: newClinic ? newClinic.clinicId : null,
            updatedAt: new Date()
        };

        const updateResult = await Staff.updateMany(
            { staffId: { $in: staffIds.map(id => parseInt(id)) } },
            { $set: updateData }
        );

        // Get updated staff information
        const updatedStaff = await Staff.find({
            staffId: { $in: staffIds.map(id => parseInt(id)) }
        })
        .select('staffId firstName lastName email phoneNumber jobTitle clinicId')
        .lean();

        const responseData = {
            clinic: {
                clinicId: clinic.clinicId,
                clinicName: clinic.clinicName
            },
            newClinic: newClinic ? {
                clinicId: newClinic.clinicId,
                clinicName: newClinic.clinicName
            } : null,
            updatedStaff: updatedStaff,
            removedCount: updateResult.modifiedCount
        };

        const message = newClinic
            ? `${updateResult.modifiedCount} staff members moved to ${newClinic.clinicName} successfully`
            : `${updateResult.modifiedCount} staff members removed from clinic successfully`;

        return sendResponse(res, 200, true, message, responseData);
    } catch (error) {
        console.error("Remove staff from clinic error:", error);
        return sendResponse(res, 500, false, error.message);
    }
};
