import { sendResponse } from '../utils/responseHandler.js';
import Appointment from '../models/appointment.model.js';
import Pet from '../models/pet.model.js';
import Client from '../models/client.model.js';
import Staff from '../models/staff.model.js';
import AppointmentCategory from '../models/appointmentCategory.model.js';
import { openai } from '../config/ai.js';

/**
 * AI-powered appointment scheduling suggestions
 */
export const getAppointmentSuggestions = async (req, res) => {
    try {
        const { clinicId } = req.user;
        const { petId, symptoms, urgency = 'normal', preferredDate } = req.body;

        if (!petId || !symptoms) {
            return sendResponse(res, 400, false, "Pet ID and symptoms are required");
        }

        // Get pet and client information
        const pet = await Pet.findOne({ petId }).lean();
        if (!pet) {
            return sendResponse(res, 404, false, "Pet not found");
        }

        const client = await Client.findOne({ clientId: pet.clientId }).lean();

        // Get available appointment categories for the clinic
        const categories = await AppointmentCategory.find({ 
            clinicId: clinicId || pet.clinicId 
        }).lean();

        // Get pet's appointment history
        const appointmentHistory = await Appointment.find({ 
            petId,
            status: 'completed'
        })
        .select('appointmentDate diagnosis treatment appointmentCategories')
        .sort({ appointmentDate: -1 })
        .limit(5)
        .lean();

        // Prepare AI prompt
        const prompt = `
        As a veterinary AI assistant, analyze the following case and suggest appropriate appointment categories:

        Pet Information:
        - Name: ${pet.petName}
        - Species: ${pet.species}
        - Breed: ${pet.breed}
        - Age: ${pet.age} months
        - Weight: ${pet.weight} kg
        - Gender: ${pet.gender}

        Current Symptoms: ${symptoms}
        Urgency Level: ${urgency}
        Preferred Date: ${preferredDate || 'Not specified'}

        Recent Medical History:
        ${appointmentHistory.map(apt => `
        - Date: ${apt.appointmentDate}
        - Diagnosis: ${apt.diagnosis || 'Not specified'}
        - Treatment: ${apt.treatment || 'Not specified'}
        `).join('\n')}

        Available Categories:
        ${categories.map(cat => `- ${cat.categoryName}: ${cat.description}`).join('\n')}

        Please provide:
        1. Top 3 recommended appointment categories with confidence scores (0-1)
        2. Estimated duration for each category
        3. Urgency assessment
        4. Brief reasoning for each recommendation
        5. Any contraindications or special considerations

        Respond in JSON format:
        {
            "suggestions": [
                {
                    "categoryName": "string",
                    "confidence": number,
                    "estimatedDuration": number,
                    "reasoning": "string",
                    "urgency": "low|normal|high|urgent",
                    "contraindications": ["string"]
                }
            ],
            "overallAssessment": "string",
            "recommendedTimeframe": "string"
        }
        `;

        // Call AI service
        const aiResponse = await openai.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "You are an expert veterinary AI assistant. Provide accurate, helpful suggestions based on symptoms and medical history."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.3,
            max_tokens: 1000
        });

        let suggestions;
        try {
            suggestions = JSON.parse(aiResponse.choices[0].message.content);
        } catch (parseError) {
            console.error("Error parsing AI response:", parseError);
            return sendResponse(res, 500, false, "Error processing AI suggestions");
        }

        // Match suggestions with actual category IDs
        const enhancedSuggestions = suggestions.suggestions.map(suggestion => {
            const matchedCategory = categories.find(cat => 
                cat.categoryName.toLowerCase().includes(suggestion.categoryName.toLowerCase()) ||
                suggestion.categoryName.toLowerCase().includes(cat.categoryName.toLowerCase())
            );

            return {
                ...suggestion,
                categoryId: matchedCategory?.appointmentCategoryId || null,
                available: !!matchedCategory
            };
        });

        // Get available staff for recommended categories
        const staffSuggestions = await Staff.find({
            clinicId: clinicId || pet.clinicId,
            status: 1,
            specializations: { 
                $in: enhancedSuggestions.map(s => s.categoryName.toLowerCase()) 
            }
        })
        .select('staffId firstName lastName jobTitle specializations')
        .lean();

        return sendResponse(res, 200, true, "AI suggestions generated successfully", {
            petInfo: {
                petId: pet.petId,
                petName: pet.petName,
                species: pet.species,
                breed: pet.breed
            },
            suggestions: enhancedSuggestions,
            overallAssessment: suggestions.overallAssessment,
            recommendedTimeframe: suggestions.recommendedTimeframe,
            recommendedStaff: staffSuggestions,
            confidence: enhancedSuggestions.reduce((acc, s) => acc + s.confidence, 0) / enhancedSuggestions.length
        });

    } catch (error) {
        console.error("Error generating AI appointment suggestions:", error);
        return sendResponse(res, 500, false, "Error generating AI suggestions: " + error.message);
    }
};

/**
 * AI-powered medical recommendations
 */
export const getMedicalRecommendations = async (req, res) => {
    try {
        const { appointmentId, symptoms, currentFindings, requestedAdvice } = req.body;

        if (!appointmentId) {
            return sendResponse(res, 400, false, "Appointment ID is required");
        }

        // Get appointment details
        const appointment = await Appointment.findOne({ appointmentId })
            .populate('petId')
            .lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Prepare medical context
        const prompt = `
        As a veterinary medical AI assistant, provide recommendations for the following case:

        Patient: ${appointment.petName} (${appointment.petSpecies}, ${appointment.petBreed})
        Age: ${appointment.petAge} months
        Weight: ${appointment.petWeight} kg

        Current Symptoms: ${symptoms}
        Current Findings: ${currentFindings || 'Not specified'}
        Specific Question: ${requestedAdvice || 'General recommendations needed'}

        Categories being addressed: ${appointment.appointmentCategories.map(cat => cat.categoryName).join(', ')}

        Please provide:
        1. Differential diagnoses (top 3 most likely)
        2. Recommended diagnostic tests
        3. Treatment options
        4. Medication suggestions with dosages
        5. Follow-up recommendations
        6. Warning signs to watch for

        Respond in JSON format with confidence scores and reasoning.
        `;

        const aiResponse = await openai.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "You are an expert veterinary medical AI. Provide evidence-based recommendations while emphasizing the need for professional veterinary judgment."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.2,
            max_tokens: 1500
        });

        let recommendations;
        try {
            recommendations = JSON.parse(aiResponse.choices[0].message.content);
        } catch (parseError) {
            console.error("Error parsing AI medical recommendations:", parseError);
            return sendResponse(res, 500, false, "Error processing AI recommendations");
        }

        return sendResponse(res, 200, true, "Medical recommendations generated successfully", {
            appointmentId,
            petInfo: {
                name: appointment.petName,
                species: appointment.petSpecies,
                breed: appointment.petBreed,
                age: appointment.petAge,
                weight: appointment.petWeight
            },
            recommendations,
            disclaimer: "These are AI-generated suggestions. Always use professional veterinary judgment and follow clinic protocols."
        });

    } catch (error) {
        console.error("Error generating medical recommendations:", error);
        return sendResponse(res, 500, false, "Error generating medical recommendations: " + error.message);
    }
};

/**
 * AI-powered workflow automation
 */
export const triggerWorkflowAutomation = async (req, res) => {
    try {
        const { appointmentId, trigger, context = {} } = req.body;

        if (!appointmentId || !trigger) {
            return sendResponse(res, 400, false, "Appointment ID and trigger are required");
        }

        const appointment = await Appointment.findOne({ appointmentId }).lean();
        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        let automationResult = {};

        switch (trigger) {
            case 'appointment_created':
                automationResult = await automateAppointmentCreation(appointment);
                break;
            case 'category_completed':
                automationResult = await automateCategoryCompletion(appointment, context);
                break;
            case 'symptoms_updated':
                automationResult = await automateSymptomAnalysis(appointment, context);
                break;
            default:
                return sendResponse(res, 400, false, "Invalid automation trigger");
        }

        return sendResponse(res, 200, true, "Workflow automation completed", automationResult);

    } catch (error) {
        console.error("Error in workflow automation:", error);
        return sendResponse(res, 500, false, "Error in workflow automation: " + error.message);
    }
};

/**
 * Get AI-powered clinic insights
 */
export const getClinicInsights = async (req, res) => {
    try {
        const { clinicId } = req.user;
        const { period = 'monthly' } = req.query;

        if (!clinicId) {
            return sendResponse(res, 400, false, "Clinic context required");
        }

        // Get clinic data for analysis
        const endDate = new Date();
        const startDate = new Date();
        
        switch (period) {
            case 'weekly':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case 'monthly':
                startDate.setMonth(endDate.getMonth() - 1);
                break;
            case 'quarterly':
                startDate.setMonth(endDate.getMonth() - 3);
                break;
        }

        // Get appointments data
        const appointments = await Appointment.find({
            clinicId,
            appointmentDate: { $gte: startDate, $lte: endDate }
        }).lean();

        // Analyze patterns and generate insights
        const insights = {
            totalAppointments: appointments.length,
            completedAppointments: appointments.filter(apt => apt.status === 'completed').length,
            cancelledAppointments: appointments.filter(apt => apt.status === 'cancelled').length,
            averageDuration: appointments.reduce((acc, apt) => acc + (apt.actualDuration || 0), 0) / appointments.length,
            popularCategories: getPopularCategories(appointments),
            busyDays: getBusyDays(appointments),
            recommendations: generateInsightRecommendations(appointments)
        };

        return sendResponse(res, 200, true, "Clinic insights generated successfully", insights);

    } catch (error) {
        console.error("Error generating clinic insights:", error);
        return sendResponse(res, 500, false, "Error generating clinic insights: " + error.message);
    }
};

// Helper functions
const automateAppointmentCreation = async (appointment) => {
    // AI logic for appointment creation automation
    return { message: "Appointment creation automated", tasks: [] };
};

const automateCategoryCompletion = async (appointment, context) => {
    // AI logic for category completion automation
    return { message: "Category completion automated", nextSteps: [] };
};

const automateSymptomAnalysis = async (appointment, context) => {
    // AI logic for symptom analysis automation
    return { message: "Symptom analysis automated", insights: [] };
};

const getPopularCategories = (appointments) => {
    const categoryCount = {};
    appointments.forEach(apt => {
        apt.appointmentCategories.forEach(cat => {
            categoryCount[cat.categoryName] = (categoryCount[cat.categoryName] || 0) + 1;
        });
    });
    return Object.entries(categoryCount).sort((a, b) => b[1] - a[1]).slice(0, 5);
};

const getBusyDays = (appointments) => {
    const dayCount = {};
    appointments.forEach(apt => {
        const day = new Date(apt.appointmentDate).toLocaleDateString('en-US', { weekday: 'long' });
        dayCount[day] = (dayCount[day] || 0) + 1;
    });
    return Object.entries(dayCount).sort((a, b) => b[1] - a[1]);
};

const generateInsightRecommendations = (appointments) => {
    const recommendations = [];
    
    if (appointments.length > 0) {
        const completionRate = appointments.filter(apt => apt.status === 'completed').length / appointments.length;
        if (completionRate < 0.8) {
            recommendations.push({
                type: 'efficiency',
                title: 'Improve Appointment Completion Rate',
                description: 'Consider reviewing scheduling practices and follow-up procedures',
                impact: 'high'
            });
        }
    }
    
    return recommendations;
};
