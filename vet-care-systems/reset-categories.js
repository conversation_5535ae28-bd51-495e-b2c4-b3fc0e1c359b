/**
 * Reset and Reseed Appointment Categories Script
 * This script clears existing categories and reseeds them properly
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import AppointmentCategory from './models/appointmentCategory.model.js';
import CategoryService from './models/categoryService.model.js';

// Load environment variables
config({ path: '.env.development.local' });

const MONGODB_URI = process.env.DB_URI;

const defaultCategories = [
    {
        name: "Vaccination",
        description: "Preventive vaccination services for pets",
        icon: "syringe",
        color: "#10B981", // Green
        displayOrder: 1,
        estimatedDuration: 15,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Consultation",
        description: "General health consultation and examination",
        icon: "stethoscope",
        color: "#3B82F6", // Blue
        displayOrder: 2,
        estimatedDuration: 30,
        defaultStaffRoles: ["veterinarian"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Grooming",
        description: "Pet grooming and hygiene services",
        icon: "scissors",
        color: "#F59E0B", // Amber
        displayOrder: 3,
        estimatedDuration: 60,
        defaultStaffRoles: ["groomer", "assistant"],
        requiresEquipment: true,
        createdBy: 1001
    },
    {
        name: "Day Care",
        description: "Pet day care and supervision services",
        icon: "home",
        color: "#8B5CF6", // Purple
        displayOrder: 4,
        estimatedDuration: 480, // 8 hours
        defaultStaffRoles: ["assistant", "vet_tech"],
        createdBy: 1001
    },
    {
        name: "Dog Walking",
        description: "Professional dog walking and exercise services",
        icon: "walking",
        color: "#06B6D4", // Cyan
        displayOrder: 5,
        estimatedDuration: 30,
        defaultStaffRoles: ["assistant"],
        createdBy: 1001
    },
    {
        name: "Cat Walking",
        description: "Professional cat walking and exercise services",
        icon: "walking",
        color: "#06B6D4", // Cyan
        displayOrder: 6,
        estimatedDuration: 30,
        defaultStaffRoles: ["assistant"],
        createdBy: 1001
    },
    {
        name: "Imaging",
        description: "X-ray, ultrasound, and other imaging services",
        icon: "camera",
        color: "#6366F1", // Indigo
        displayOrder: 7,
        estimatedDuration: 45,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Surgery",
        description: "Surgical procedures and operations",
        icon: "scissors",
        color: "#DC2626", // Red
        displayOrder: 8,
        estimatedDuration: 120,
        defaultStaffRoles: ["veterinarian"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Laboratory",
        description: "Laboratory tests and diagnostics",
        icon: "flask",
        color: "#059669", // Emerald
        displayOrder: 9,
        estimatedDuration: 30,
        defaultStaffRoles: ["vet_tech", "lab_tech"],
        requiresEquipment: true,
        createdBy: 1001
    },
    {
        name: "Dental",
        description: "Dental care and oral health services",
        icon: "tooth",
        color: "#0891B2", // Sky
        displayOrder: 10,
        estimatedDuration: 45,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Emergency",
        description: "Emergency and urgent care services",
        icon: "ambulance",
        color: "#EF4444", // Red
        displayOrder: 11,
        estimatedDuration: 60,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Boarding",
        description: "Pet boarding and overnight care services",
        icon: "bed",
        color: "#7C3AED", // Violet
        displayOrder: 12,
        estimatedDuration: 1440, // 24 hours
        defaultStaffRoles: ["assistant", "vet_tech"],
        createdBy: 1001
    },
    {
        name: "Wellness Check",
        description: "Routine wellness and health check-ups",
        icon: "heart",
        color: "#EC4899", // Pink
        displayOrder: 13,
        estimatedDuration: 30,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        createdBy: 1001
    },
    {
        name: "Pharmacy",
        description: "Medication dispensing and pharmaceutical services",
        icon: "pill",
        color: "#14B8A6", // Teal
        displayOrder: 14,
        estimatedDuration: 15,
        defaultStaffRoles: ["vet_tech", "veterinarian"],
        createdBy: 1001
    },
    {
        name: "Behavioral Training",
        description: "Pet behavioral training and modification services",
        icon: "brain",
        color: "#F97316", // Orange
        displayOrder: 15,
        estimatedDuration: 60,
        defaultStaffRoles: ["assistant"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Nutrition Counseling",
        description: "Pet nutrition consultation and dietary planning",
        icon: "apple",
        color: "#84CC16", // Lime
        displayOrder: 16,
        estimatedDuration: 30,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        createdBy: 1001
    },
    {
        name: "Microchipping",
        description: "Pet identification microchip implantation",
        icon: "chip",
        color: "#6B7280", // Gray
        displayOrder: 17,
        estimatedDuration: 15,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        createdBy: 1001
    },
    {
        name: "Euthanasia",
        description: "End-of-life care and euthanasia services",
        icon: "heart-off",
        color: "#374151", // Gray
        displayOrder: 18,
        estimatedDuration: 45,
        defaultStaffRoles: ["veterinarian"],
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Physiotherapy",
        description: "Physical therapy and rehabilitation services",
        icon: "activity",
        color: "#0EA5E9", // Sky
        displayOrder: 19,
        estimatedDuration: 45,
        defaultStaffRoles: ["vet_tech"],
        requiresEquipment: true,
        requiresQualification: true,
        createdBy: 1001
    },
    {
        name: "Follow-up",
        description: "Follow-up appointments and check-ins",
        icon: "calendar",
        color: "#8B5CF6", // Purple
        displayOrder: 20,
        estimatedDuration: 20,
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        createdBy: 1001
    }
];

async function resetAndReseedCategories() {
    try {
        console.log('🔌 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ Connected to MongoDB');

        // Clear existing categories and services
        console.log('🗑️  Clearing existing appointment categories...');
        await AppointmentCategory.deleteMany({});

        console.log('🗑️  Clearing existing category services...');
        await CategoryService.deleteMany({});

        // Create new categories one by one to ensure auto-increment works
        console.log('📋 Creating appointment categories...');
        const createdCategories = [];

        for (const categoryData of defaultCategories) {
            try {
                const category = await AppointmentCategory.create(categoryData);
                createdCategories.push(category);
                console.log(`   ✅ Created: ${category.name} (ID: ${category.appointmentCategoryId})`);
            } catch (error) {
                console.error(`   ❌ Failed to create ${categoryData.name}:`, error.message);
            }
        }

        console.log(`✅ Successfully created ${createdCategories.length} appointment categories`);

        console.log('\n🎉 RESET AND RESEED COMPLETED SUCCESSFULLY!');
        console.log('You can now restart the server to seed the category services.');

    } catch (error) {
        console.error('❌ Reset failed:', error);
        console.error('Stack trace:', error.stack);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

// Run the reset
resetAndReseedCategories().catch(console.error);
