/**
 * Test JSON Storage API
 * 
 * This script demonstrates how to use the optimized appointment API
 * with JSON storage for reduced data complexity.
 */

const API_BASE = 'http://localhost:5500/api';

// Sample optimized appointment data
const sampleAppointment = {
    petId: 2001,
    clinicId: 1001,
    staffId: 3001,
    clientId: 4001,
    appointmentDate: new Date('2024-01-15T10:00:00Z'),
    priority: 'normal',
    
    // All complex data stored as JSON objects
    appointmentData: {
        appointmentTypes: [{ appointmentTypeId: 1, name: 'Consultation', duration: 30 }],
        reason: 'Annual checkup',
        notes: 'Routine examination',
        isAfterHours: false
    },
    
    petData: {
        petName: 'Buddy',
        species: 'Dog',
        breed: 'Golden Retriever',
        age: 5,
        weight: 25.5,
        allergies: ['peanuts', 'chicken'],
        chronicConditions: ['hip dysplasia']
    },
    
    clientData: {
        firstName: 'John',
        lastName: 'Doe',
        phone: '+**********',
        email: '<EMAIL>',
        address: '123 Main St'
    },
    
    staffData: {
        firstName: 'Dr. <PERSON>',
        lastName: '<PERSON>',
        specialization: 'Small Animals',
        licenseNumber: 'VET12345'
    },
    
    servicesData: [
        { serviceId: 1001, name: 'Physical Examination', category: 'examination', cost: 50, status: 'completed' },
        { serviceId: 1002, name: 'Vaccination', category: 'prevention', cost: 30, status: 'completed' }
    ],
    
    medicalData: {
        vitalSigns: { temperature: 101.5, heartRate: 120, weight: 25.5 },
        diagnosis: 'Healthy',
        treatment: 'Routine care',
        medications: [{ name: 'Vitamins', dosage: '1 tablet daily' }]
    },
    
    billingData: {
        baseAmount: 80,
        afterHoursCharge: 0,
        discounts: [],
        paymentStatus: 'paid'
    }
};

async function testCreateOptimizedAppointment() {
    console.log('🔄 Testing optimized appointment creation...');
    
    try {
        const response = await fetch(`${API_BASE}/optimized-appointments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(sampleAppointment)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Optimized appointment created successfully!');
            console.log('📊 Response data:', JSON.stringify(result.data, null, 2));
            return result.data.appointmentId;
        } else {
            console.log('❌ Failed to create appointment:', result.message);
            return null;
        }
    } catch (error) {
        console.error('❌ Error creating appointment:', error.message);
        return null;
    }
}

async function testGetOptimizedAppointments() {
    console.log('\n🔄 Testing optimized appointment retrieval...');
    
    try {
        // Test basic retrieval
        const response = await fetch(`${API_BASE}/optimized-appointments?limit=5`);
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Retrieved appointments successfully!');
            console.log(`📊 Found ${result.data.data.length} appointments`);
            
            // Show first appointment structure
            if (result.data.data.length > 0) {
                const firstAppointment = result.data.data[0];
                console.log('\n📋 First appointment structure:');
                console.log('- appointmentId:', firstAppointment.appointmentId);
                console.log('- petData.petName:', firstAppointment.petData?.petName);
                console.log('- clientData.firstName:', firstAppointment.clientData?.firstName);
                console.log('- servicesData count:', firstAppointment.servicesData?.length || 0);
                console.log('- totalCost:', firstAppointment.totalCost);
                console.log('- keywords:', firstAppointment.keywords);
                console.log('- categories:', firstAppointment.categories);
            }
        } else {
            console.log('❌ Failed to retrieve appointments:', result.message);
        }
    } catch (error) {
        console.error('❌ Error retrieving appointments:', error.message);
    }
}

async function testAdvancedQueries() {
    console.log('\n🔄 Testing advanced JSON queries...');
    
    try {
        // Test 1: Query by pet species
        console.log('\n1️⃣ Query by pet species (Dog):');
        const speciesResponse = await fetch(`${API_BASE}/optimized-appointments?petSpecies=Dog`);
        const speciesResult = await speciesResponse.json();
        
        if (speciesResponse.ok) {
            console.log(`✅ Found ${speciesResult.data.data.length} dog appointments`);
        }
        
        // Test 2: Query by service category
        console.log('\n2️⃣ Query by service category (examination):');
        const categoryResponse = await fetch(`${API_BASE}/optimized-appointments?serviceCategory=examination`);
        const categoryResult = await categoryResponse.json();
        
        if (categoryResponse.ok) {
            console.log(`✅ Found ${categoryResult.data.data.length} examination appointments`);
        }
        
        // Test 3: Query by cost range
        console.log('\n3️⃣ Query by cost range ($50-$100):');
        const costResponse = await fetch(`${API_BASE}/optimized-appointments?minCost=50&maxCost=100`);
        const costResult = await costResponse.json();
        
        if (costResponse.ok) {
            console.log(`✅ Found ${costResult.data.data.length} appointments in cost range`);
        }
        
        // Test 4: Query pets with allergies
        console.log('\n4️⃣ Query pets with allergies:');
        const allergiesResponse = await fetch(`${API_BASE}/optimized-appointments?hasAllergies=true`);
        const allergiesResult = await allergiesResponse.json();
        
        if (allergiesResponse.ok) {
            console.log(`✅ Found ${allergiesResult.data.data.length} appointments for pets with allergies`);
        }
        
    } catch (error) {
        console.error('❌ Error in advanced queries:', error.message);
    }
}

async function testSearch() {
    console.log('\n🔄 Testing search functionality...');
    
    try {
        const searchResponse = await fetch(`${API_BASE}/optimized-appointments/search`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query: 'Golden Retriever' })
        });
        
        const searchResult = await searchResponse.json();
        
        if (searchResponse.ok) {
            console.log(`✅ Search found ${searchResult.data.length} results for "Golden Retriever"`);
        } else {
            console.log('❌ Search failed:', searchResult.message);
        }
    } catch (error) {
        console.error('❌ Error in search:', error.message);
    }
}

async function testAnalytics() {
    console.log('\n🔄 Testing analytics...');
    
    try {
        const analyticsResponse = await fetch(`${API_BASE}/optimized-appointments/analytics?clinicId=1001`);
        const analyticsResult = await analyticsResponse.json();
        
        if (analyticsResponse.ok) {
            console.log('✅ Analytics retrieved successfully!');
            console.log('📊 Analytics data:', JSON.stringify(analyticsResult.data, null, 2));
        } else {
            console.log('❌ Analytics failed:', analyticsResult.message);
        }
    } catch (error) {
        console.error('❌ Error in analytics:', error.message);
    }
}

async function runTests() {
    console.log('🚀 Testing JSON Storage API for Veterinary System');
    console.log('=================================================\n');
    
    // Test creation
    const appointmentId = await testCreateOptimizedAppointment();
    
    // Wait a moment for data to be processed
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test retrieval
    await testGetOptimizedAppointments();
    
    // Test advanced queries
    await testAdvancedQueries();
    
    // Test search
    await testSearch();
    
    // Test analytics
    await testAnalytics();
    
    console.log('\n✅ All tests completed!');
    console.log('\n📊 Summary: JSON storage provides:');
    console.log('   • Single API call for complete appointment data');
    console.log('   • Flexible querying within JSON fields');
    console.log('   • Fast keyword and category searches');
    console.log('   • Powerful analytics capabilities');
    console.log('   • Reduced database complexity');
}

// Run the tests
runTests().catch(console.error);
