/**
 * Test the unified advanced appointment workflow
 */

const API_BASE_URL = 'http://localhost:5000/api';

class UnifiedAdvancedWorkflowTester {
    constructor() {
        this.testAppointmentId = null;
        this.testStaffId = null;
        this.testCategoryId = null;
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        const url = `${API_BASE_URL}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-token' // You'll need a valid token
            }
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(url, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${result.message || 'Request failed'}`);
            }
            
            return result;
        } catch (error) {
            console.error(`API call failed for ${method} ${endpoint}:`, error.message);
            throw error;
        }
    }

    async testGetAppointmentData() {
        console.log('\n🔍 Testing: Get appointment data with full structure...');
        
        try {
            // Get appointments to find one to test with
            const appointmentsResponse = await this.apiCall('/appointments?limit=1');
            
            if (!appointmentsResponse.success || !appointmentsResponse.data?.data?.length) {
                console.log('❌ No appointments found to test with');
                return false;
            }

            const appointment = appointmentsResponse.data.data[0];
            this.testAppointmentId = appointment.appointmentId;
            
            // Get detailed appointment data
            const detailResponse = await this.apiCall(`/appointments/${this.testAppointmentId}`);
            
            if (!detailResponse.success) {
                console.log('❌ Failed to get appointment details');
                return false;
            }

            const appointmentData = detailResponse.data;
            
            console.log(`✅ Found test appointment: ${this.testAppointmentId}`);
            console.log(`   Pet: ${appointmentData.petName}`);
            console.log(`   Client: ${appointmentData.clientName}`);
            console.log(`   Staff: ${appointmentData.staffName}`);
            console.log(`   Status: ${appointmentData.status}`);
            console.log(`   Appointment Types: ${appointmentData.appointmentTypes?.length || 0}`);
            console.log(`   Service Categories: ${appointmentData.serviceCategories?.length || 0}`);
            console.log(`   Tasks: ${appointmentData.tasks?.length || 0}`);
            console.log(`   Total Services: ${appointmentData.totalServicesCount || 0}`);
            console.log(`   Completed Services: ${appointmentData.completedServicesCount || 0}`);
            console.log(`   Completion: ${appointmentData.completionPercentage || 0}%`);
            console.log(`   Billing: ${appointmentData.billing?.currency || 'KES'} ${appointmentData.billing?.totalAmount || 0}`);
            
            // Store category ID for testing
            if (appointmentData.appointmentTypes?.length > 0) {
                this.testCategoryId = appointmentData.appointmentTypes[0].categoryId;
            }
            
            return true;
        } catch (error) {
            console.error('❌ Failed to get appointment data:', error.message);
            return false;
        }
    }

    async testStaffAssignment() {
        console.log('\n👨‍⚕️ Testing: Staff assignment to appointment types...');
        
        if (!this.testAppointmentId || !this.testCategoryId) {
            console.log('❌ Missing test data');
            return false;
        }

        try {
            // Get available staff
            const staffResponse = await this.apiCall('/staff?status=1&limit=5');
            
            if (!staffResponse.success || !staffResponse.data?.data?.length) {
                console.log('❌ No staff found');
                return false;
            }

            this.testStaffId = staffResponse.data.data[0].staffId;
            const staffName = `${staffResponse.data.data[0].firstName} ${staffResponse.data.data[0].lastName}`;

            // Assign staff to appointment type
            const assignResponse = await this.apiCall(
                `/appointments/${this.testAppointmentId}/assign-staff`,
                'POST',
                {
                    categoryId: this.testCategoryId,
                    staffId: this.testStaffId
                }
            );

            if (assignResponse.success) {
                console.log(`✅ Successfully assigned staff: ${staffName} to category: ${this.testCategoryId}`);
                return true;
            } else {
                console.log('❌ Failed to assign staff:', assignResponse.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to assign staff:', error.message);
            return false;
        }
    }

    async testAddService() {
        console.log('\n🩺 Testing: Add service based on appointment type...');
        
        if (!this.testAppointmentId || !this.testCategoryId) {
            console.log('❌ Missing test data');
            return false;
        }

        try {
            const serviceResponse = await this.apiCall(
                `/appointments/${this.testAppointmentId}/services`,
                'POST',
                {
                    categoryId: this.testCategoryId,
                    serviceName: 'Test Service from Unified Workflow',
                    price: 1500,
                    currency: 'KES',
                    notes: 'Service created through unified advanced workflow testing'
                }
            );

            if (serviceResponse.success) {
                console.log('✅ Successfully added service');
                console.log('   Service: Test Service from Unified Workflow');
                console.log('   Price: KES 1500');
                console.log('   Progress updated automatically');
                return true;
            } else {
                console.log('❌ Failed to add service:', serviceResponse.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to add service:', error.message);
            return false;
        }
    }

    async testAddTask() {
        console.log('\n📋 Testing: Add task to appointment...');
        
        if (!this.testAppointmentId || !this.testStaffId) {
            console.log('❌ Missing test data');
            return false;
        }

        try {
            const taskResponse = await this.apiCall(
                `/appointments/${this.testAppointmentId}/tasks`,
                'POST',
                {
                    taskName: 'Follow-up Call',
                    description: 'Call client in 3 days to check on pet recovery',
                    priority: 'normal',
                    assignedTo: this.testStaffId
                }
            );

            if (taskResponse.success) {
                console.log('✅ Successfully added task');
                console.log('   Task: Follow-up Call');
                console.log('   Priority: normal');
                console.log('   Assigned to staff');
                return true;
            } else {
                console.log('❌ Failed to add task:', taskResponse.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to add task:', error.message);
            return false;
        }
    }

    async testUpdateAppointment() {
        console.log('\n📝 Testing: Update appointment notes and status...');
        
        if (!this.testAppointmentId) {
            console.log('❌ Missing test appointment ID');
            return false;
        }

        try {
            const updateResponse = await this.apiCall(
                `/appointments/${this.testAppointmentId}`,
                'PUT',
                {
                    generalNotes: 'Updated through unified advanced workflow - comprehensive examination completed',
                    recommendations: 'Continue medication, follow-up in 1 week, monitor symptoms',
                    status: 'in_progress',
                    completionStatus: 'in_progress'
                }
            );

            if (updateResponse.success) {
                console.log('✅ Successfully updated appointment');
                console.log('   Notes and recommendations updated');
                console.log('   Status updated to in_progress');
                return true;
            } else {
                console.log('❌ Failed to update appointment:', updateResponse.message);
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to update appointment:', error.message);
            return false;
        }
    }

    async testFinalVerification() {
        console.log('\n🔍 Testing: Final verification of all changes...');
        
        if (!this.testAppointmentId) {
            console.log('❌ Missing test appointment ID');
            return false;
        }

        try {
            const finalResponse = await this.apiCall(`/appointments/${this.testAppointmentId}`);
            
            if (!finalResponse.success) {
                console.log('❌ Failed to get final appointment data');
                return false;
            }

            const appointment = finalResponse.data;
            
            console.log('✅ Final verification results:');
            console.log(`   Staff assignments: ${appointment.appointmentTypes?.filter(t => t.assignedStaff).length || 0}`);
            console.log(`   Service categories: ${appointment.serviceCategories?.length || 0}`);
            console.log(`   Total services: ${appointment.totalServicesCount || 0}`);
            console.log(`   Tasks: ${appointment.tasks?.length || 0}`);
            console.log(`   Completion: ${appointment.completionPercentage || 0}%`);
            console.log(`   Billing total: ${appointment.billing?.currency || 'KES'} ${appointment.billing?.totalAmount || 0}`);
            console.log(`   Notes: ${appointment.generalNotes ? 'Updated' : 'Not set'}`);
            console.log(`   Recommendations: ${appointment.recommendations ? 'Updated' : 'Not set'}`);
            
            return true;
        } catch (error) {
            console.error('❌ Failed final verification:', error.message);
            return false;
        }
    }

    async runTest() {
        console.log('🚀 Starting Unified Advanced Appointment Workflow Test');
        console.log('=====================================================\n');

        try {
            const tests = [
                { name: 'Get Appointment Data', fn: () => this.testGetAppointmentData() },
                { name: 'Staff Assignment', fn: () => this.testStaffAssignment() },
                { name: 'Add Service', fn: () => this.testAddService() },
                { name: 'Add Task', fn: () => this.testAddTask() },
                { name: 'Update Appointment', fn: () => this.testUpdateAppointment() },
                { name: 'Final Verification', fn: () => this.testFinalVerification() }
            ];

            let passed = 0;
            let failed = 0;

            for (const test of tests) {
                const result = await test.fn();
                if (result) {
                    passed++;
                } else {
                    failed++;
                }
            }

            console.log('\n📊 TEST RESULTS');
            console.log('================');
            console.log(`✅ Passed: ${passed}`);
            console.log(`❌ Failed: ${failed}`);
            console.log(`📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);

            if (failed === 0) {
                console.log('\n🎉 ALL TESTS PASSED! Unified advanced workflow is working correctly.');
                console.log('✨ Features verified:');
                console.log('   - Complete appointment data capture');
                console.log('   - Staff assignment to appointment types');
                console.log('   - Service creation based on appointment types');
                console.log('   - Task management and assignment');
                console.log('   - Progress tracking and completion percentage');
                console.log('   - Billing calculation and updates');
                console.log('   - Notes and recommendations management');
            } else {
                console.log('\n⚠️  Some tests failed. Please check the implementation.');
            }

        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
        }
    }
}

// Run the test
const tester = new UnifiedAdvancedWorkflowTester();
tester.runTest().catch(console.error);
