/**
 * Test New Appointment System with Service Categories
 * 
 * This script demonstrates the new restructured appointment system:
 * - ServiceCategory table (instead of appointmentTypeService)
 * - Service table with serviceCategoryId
 * - Appointment has serviceCategories array
 * - Each service has notes and staff tracking
 * - General appointment has staffInCharge and overall notes
 */

const API_BASE = 'http://localhost:5500/api';

async function testServiceCategories() {
    console.log('🔄 Testing Service Categories...');
    
    try {
        // Get all service categories
        const response = await fetch(`${API_BASE}/service-categories?includeServices=true`);
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Service categories retrieved successfully!');
            console.log(`📊 Found ${result.data.data.length} categories`);
            
            result.data.data.forEach(category => {
                console.log(`   - ${category.name}: ${category.serviceCount || 0} services`);
            });
            
            return result.data.data;
        } else {
            console.log('❌ Failed to get service categories:', result.message);
            return [];
        }
    } catch (error) {
        console.error('❌ Error getting service categories:', error.message);
        return [];
    }
}

async function testCreateNewAppointment() {
    console.log('\n🔄 Testing New Appointment Creation...');
    
    try {
        // Sample appointment with service categories
        const appointmentData = {
            petId: 2001,
            clientId: 4001,
            clinicId: 1001,
            staffInCharge: 1020, // Currently logged in staff
            appointmentDate: new Date(),
            priority: 'normal',
            serviceCategories: [
                {
                    serviceCategoryId: 1001, // Consultation
                    services: [
                        {
                            serviceId: 1001, // Will be created if doesn't exist
                            notes: 'General health checkup',
                            price: 50
                        }
                    ],
                    categoryNotes: 'Routine consultation for annual checkup'
                },
                {
                    serviceCategoryId: 1002, // Vaccination
                    services: [
                        {
                            serviceId: 1002,
                            notes: 'Annual vaccination due',
                            price: 30
                        }
                    ],
                    categoryNotes: 'Vaccination schedule up to date'
                }
            ],
            generalNotes: 'Patient appears healthy, no immediate concerns',
            recommendations: 'Continue current diet, schedule next checkup in 6 months'
        };
        
        const response = await fetch(`${API_BASE}/new-appointments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(appointmentData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ New appointment created successfully!');
            console.log('📊 Appointment details:');
            console.log(`   - Appointment ID: ${result.data.appointmentId}`);
            console.log(`   - Status: ${result.data.status}`);
            console.log(`   - Staff in Charge: ${result.data.staffInCharge}`);
            console.log(`   - Service Categories: ${result.data.serviceCategories.length}`);
            console.log(`   - Total Cost: $${result.data.billing.totalAmount}`);
            console.log(`   - Completion: ${result.data.completionStatus}`);
            
            return result.data.appointmentId;
        } else {
            console.log('❌ Failed to create appointment:', result.message);
            return null;
        }
    } catch (error) {
        console.error('❌ Error creating appointment:', error.message);
        return null;
    }
}

async function testGetCurrentAppointments() {
    console.log('\n🔄 Testing Current Appointments...');
    
    try {
        const response = await fetch(`${API_BASE}/new-appointments/current?clinicId=1001`);
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Current appointments retrieved successfully!');
            console.log(`📊 Found ${result.data.length} current appointments`);
            
            result.data.forEach(appointment => {
                console.log(`   - ID: ${appointment.appointmentId}, Status: ${appointment.status}`);
                console.log(`     Completion: ${appointment.completionPercentage}% (${appointment.completedServicesCount}/${appointment.totalServicesCount} services)`);
                console.log(`     Categories: ${appointment.serviceCategories.length}`);
            });
            
            return result.data;
        } else {
            console.log('❌ Failed to get current appointments:', result.message);
            return [];
        }
    } catch (error) {
        console.error('❌ Error getting current appointments:', error.message);
        return [];
    }
}

async function testAddServiceToAppointment(appointmentId) {
    console.log('\n🔄 Testing Add Service to Appointment...');
    
    if (!appointmentId) {
        console.log('❌ No appointment ID provided');
        return;
    }
    
    try {
        const serviceData = {
            serviceCategoryId: 1003, // Laboratory
            serviceId: 1003, // Blood test
            notes: 'Complete blood count requested',
            price: 45
        };
        
        const response = await fetch(`${API_BASE}/new-appointments/${appointmentId}/services`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(serviceData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Service added to appointment successfully!');
            console.log(`📊 Updated appointment has ${result.data.serviceCategories.length} categories`);
            console.log(`   - Total services: ${result.data.serviceCategories.reduce((total, cat) => total + cat.services.length, 0)}`);
            console.log(`   - New total cost: $${result.data.billing.totalAmount}`);
        } else {
            console.log('❌ Failed to add service:', result.message);
        }
    } catch (error) {
        console.error('❌ Error adding service:', error.message);
    }
}

async function testCompleteService(appointmentId) {
    console.log('\n🔄 Testing Complete Service...');
    
    if (!appointmentId) {
        console.log('❌ No appointment ID provided');
        return;
    }
    
    try {
        // Complete the first service in the first category
        const serviceCategoryId = 1001; // Consultation
        const serviceId = 1001; // General consultation
        
        const completionData = {
            notes: 'Physical examination completed. Patient is healthy.',
            completionNotes: 'No abnormalities found during examination.'
        };
        
        const response = await fetch(
            `${API_BASE}/new-appointments/${appointmentId}/categories/${serviceCategoryId}/services/${serviceId}/complete`,
            {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(completionData)
            }
        );
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Service marked as completed successfully!');
            console.log('📊 Appointment updated with completion status');
        } else {
            console.log('❌ Failed to complete service:', result.message);
        }
    } catch (error) {
        console.error('❌ Error completing service:', error.message);
    }
}

async function testUpdateAppointmentNotes(appointmentId) {
    console.log('\n🔄 Testing Update Appointment Notes...');
    
    if (!appointmentId) {
        console.log('❌ No appointment ID provided');
        return;
    }
    
    try {
        const notesData = {
            generalNotes: 'Patient responded well to examination. All vital signs normal. Owner reports good appetite and energy levels.',
            recommendations: 'Continue current diet and exercise routine. Schedule next checkup in 6 months. Monitor for any changes in behavior or appetite.'
        };
        
        const response = await fetch(`${API_BASE}/new-appointments/${appointmentId}/notes`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(notesData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Appointment notes updated successfully!');
            console.log('📊 Notes and recommendations saved');
        } else {
            console.log('❌ Failed to update notes:', result.message);
        }
    } catch (error) {
        console.error('❌ Error updating notes:', error.message);
    }
}

async function testGetAppointmentDetails(appointmentId) {
    console.log('\n🔄 Testing Get Appointment Details...');
    
    if (!appointmentId) {
        console.log('❌ No appointment ID provided');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/new-appointments/${appointmentId}`);
        const result = await response.json();
        
        if (response.ok) {
            const appointment = result.data;
            console.log('✅ Appointment details retrieved successfully!');
            console.log('\n📋 Appointment Summary:');
            console.log(`   - ID: ${appointment.appointmentId}`);
            console.log(`   - Status: ${appointment.status}`);
            console.log(`   - Staff in Charge: ${appointment.staffInCharge}`);
            console.log(`   - Priority: ${appointment.priority}`);
            console.log(`   - Completion: ${appointment.completionPercentage}%`);
            console.log(`   - Total Cost: $${appointment.billing.totalAmount}`);
            
            console.log('\n📝 Service Categories:');
            appointment.serviceCategories.forEach((category, index) => {
                console.log(`   ${index + 1}. ${category.categoryName} (${category.categoryStatus})`);
                category.services.forEach((service, sIndex) => {
                    console.log(`      ${sIndex + 1}. ${service.serviceName} - $${service.price} (${service.status})`);
                    if (service.notes) console.log(`         Notes: ${service.notes}`);
                });
            });
            
            if (appointment.generalNotes) {
                console.log(`\n📝 General Notes: ${appointment.generalNotes}`);
            }
            if (appointment.recommendations) {
                console.log(`📝 Recommendations: ${appointment.recommendations}`);
            }
        } else {
            console.log('❌ Failed to get appointment details:', result.message);
        }
    } catch (error) {
        console.error('❌ Error getting appointment details:', error.message);
    }
}

async function runNewAppointmentSystemTests() {
    console.log('🚀 Testing New Appointment System with Service Categories');
    console.log('========================================================\n');
    
    // Test 1: Get service categories
    const categories = await testServiceCategories();
    
    // Test 2: Create new appointment
    const appointmentId = await testCreateNewAppointment();
    
    // Wait a moment for data to be processed
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 3: Get current appointments
    await testGetCurrentAppointments();
    
    if (appointmentId) {
        // Test 4: Add service to appointment
        await testAddServiceToAppointment(appointmentId);
        
        // Test 5: Complete a service
        await testCompleteService(appointmentId);
        
        // Test 6: Update appointment notes
        await testUpdateAppointmentNotes(appointmentId);
        
        // Test 7: Get final appointment details
        await testGetAppointmentDetails(appointmentId);
    }
    
    console.log('\n✅ All tests completed!');
    console.log('\n📊 New Appointment System Features Demonstrated:');
    console.log('   • Service categories instead of appointment types');
    console.log('   • Services organized by categories');
    console.log('   • Staff tracking for each service');
    console.log('   • Notes for individual services and categories');
    console.log('   • General appointment notes and recommendations');
    console.log('   • Automatic completion tracking');
    console.log('   • Default in-progress status for current appointments');
    console.log('   • Flexible service addition during appointment');
}

// Run the tests
runNewAppointmentSystemTests().catch(console.error);
