// Example: How to fetch all appointments instead of by ID

// 1. BASIC FETCH ALL APPOINTMENTS
// Frontend React Component Example
import { useQuery } from '@tanstack/react-query';
import { getAppointments } from '@/services/appointments';

const AppointmentsList = () => {
  // Fetch all appointments with basic pagination
  const { data: appointmentsResponse, isLoading, error } = useQuery({
    queryKey: ['appointments'],
    queryFn: () => getAppointments({
      page: 1,
      limit: 20 // Fetch 20 appointments at once
    })
  });

  if (isLoading) return <div>Loading appointments...</div>;
  if (error) return <div>Error loading appointments</div>;

  const appointments = appointmentsResponse?.data?.data || [];

  return (
    <div>
      <h2>All Appointments ({appointments.length})</h2>
      {appointments.map(appointment => (
        <div key={appointment.appointmentId}>
          <h3>Appointment #{appointment.appointmentId}</h3>
          <p>Status: {appointment.status}</p>
          <p>Date: {new Date(appointment.appointmentDate).toLocaleDateString()}</p>
          <p>Pet ID: {appointment.petId}</p>
          <p>Client ID: {appointment.clientId}</p>
        </div>
      ))}
    </div>
  );
};

// 2. FETCH WITH FILTERS
const FilteredAppointmentsList = () => {
  const { data: appointmentsResponse } = useQuery({
    queryKey: ['appointments', 'today'],
    queryFn: () => getAppointments({
      page: 1,
      limit: 50,
      status: 'in_progress', // Only in-progress appointments
      startDate: new Date().toISOString().split('T')[0], // Today's appointments
      sortBy: 'appointmentDate',
      sortOrder: 'asc'
    })
  });

  // ... render logic
};

// 3. FETCH CURRENT/TODAY'S APPOINTMENTS
const TodaysAppointments = () => {
  const { data: appointmentsResponse } = useQuery({
    queryKey: ['appointments', 'current'],
    queryFn: () => getAppointments({
      page: 1,
      limit: 100,
      status: 'in_progress',
      // Add today's date filter
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    })
  });

  // ... render logic
};

// 4. FETCH BY CLINIC
const ClinicAppointments = ({ clinicId }) => {
  const { data: appointmentsResponse } = useQuery({
    queryKey: ['appointments', 'clinic', clinicId],
    queryFn: () => getAppointments({
      page: 1,
      limit: 100,
      clinicId: clinicId.toString()
    })
  });

  // ... render logic
};

// 5. FETCH BY PET
const PetAppointments = ({ petId }) => {
  const { data: appointmentsResponse } = useQuery({
    queryKey: ['appointments', 'pet', petId],
    queryFn: () => getAppointments({
      page: 1,
      limit: 50,
      petId: petId.toString(),
      sortBy: 'appointmentDate',
      sortOrder: 'desc' // Most recent first
    })
  });

  // ... render logic
};

// 6. FETCH BY CLIENT
const ClientAppointments = ({ clientId }) => {
  const { data: appointmentsResponse } = useQuery({
    queryKey: ['appointments', 'client', clientId],
    queryFn: () => getAppointments({
      page: 1,
      limit: 50,
      clientId: clientId.toString(),
      sortBy: 'appointmentDate',
      sortOrder: 'desc'
    })
  });

  // ... render logic
};

// 7. FETCH WITH SEARCH
const SearchableAppointments = ({ searchTerm }) => {
  const { data: appointmentsResponse } = useQuery({
    queryKey: ['appointments', 'search', searchTerm],
    queryFn: () => getAppointments({
      page: 1,
      limit: 30,
      search: searchTerm, // Search across appointments
      sortBy: 'appointmentDate',
      sortOrder: 'desc'
    }),
    enabled: !!searchTerm // Only run query if there's a search term
  });

  // ... render logic
};

export {
  AppointmentsList,
  FilteredAppointmentsList,
  TodaysAppointments,
  ClinicAppointments,
  PetAppointments,
  ClientAppointments,
  SearchableAppointments
};
