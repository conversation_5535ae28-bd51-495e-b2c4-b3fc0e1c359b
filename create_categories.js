const http = require('http');

// Sample appointment categories to create
const categories = [
    {
        name: "Vaccination",
        description: "Preventive vaccination services for pets",
        icon: "syringe",
        color: "#10B981", // Green
        displayOrder: 1,
        estimatedDuration: 15,
        defaultPrice: 1500,
        currency: "KES",
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        tags: ["preventive", "vaccination", "immunization"]
    },
    {
        name: "Consultation",
        description: "General health consultation and examination",
        icon: "stethoscope",
        color: "#3B82F6", // Blue
        displayOrder: 2,
        estimatedDuration: 30,
        defaultPrice: 2000,
        currency: "KES",
        defaultStaffRoles: ["veterinarian"],
        requiresQualification: true,
        tags: ["consultation", "examination", "checkup"]
    },
    {
        name: "Laboratory",
        description: "Laboratory tests and diagnostics",
        icon: "flask",
        color: "#8B5CF6", // Purple
        displayOrder: 3,
        estimatedDuration: 45,
        defaultPrice: 3000,
        currency: "KES",
        defaultStaffRoles: ["lab_tech", "veterinarian"],
        requiresEquipment: true,
        requiresQualification: true,
        tags: ["laboratory", "diagnostics", "testing"]
    },
    {
        name: "Surgery",
        description: "Surgical procedures and operations",
        icon: "scissors",
        color: "#EF4444", // Red
        displayOrder: 4,
        estimatedDuration: 120,
        defaultPrice: 15000,
        currency: "KES",
        defaultStaffRoles: ["veterinarian"],
        requiresEquipment: true,
        requiresQualification: true,
        tags: ["surgery", "operation", "procedure"]
    },
    {
        name: "Grooming",
        description: "Pet grooming and hygiene services",
        icon: "scissors-alt",
        color: "#F59E0B", // Amber
        displayOrder: 5,
        estimatedDuration: 60,
        defaultPrice: 2500,
        currency: "KES",
        defaultStaffRoles: ["groomer", "assistant"],
        requiresEquipment: true,
        tags: ["grooming", "hygiene", "beauty"]
    },
    {
        name: "Dental",
        description: "Dental care and oral health services",
        icon: "tooth",
        color: "#06B6D4", // Cyan
        displayOrder: 6,
        estimatedDuration: 45,
        defaultPrice: 4000,
        currency: "KES",
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresEquipment: true,
        requiresQualification: true,
        tags: ["dental", "oral", "teeth"]
    },
    {
        name: "Emergency",
        description: "Emergency and urgent care services",
        icon: "ambulance",
        color: "#DC2626", // Red
        displayOrder: 7,
        estimatedDuration: 60,
        defaultPrice: 5000,
        currency: "KES",
        defaultStaffRoles: ["veterinarian", "vet_tech"],
        requiresQualification: true,
        isEmergency: true,
        tags: ["emergency", "urgent", "critical"]
    },
    {
        name: "Boarding",
        description: "Pet boarding and hospitalization services",
        icon: "home",
        color: "#7C3AED", // Violet
        displayOrder: 8,
        estimatedDuration: 1440, // 24 hours
        defaultPrice: 3000,
        currency: "KES",
        defaultStaffRoles: ["assistant", "vet_tech"],
        tags: ["boarding", "hospitalization", "care"]
    }
];

function createCategory(categoryData, index) {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify(categoryData);

        console.log(`\n🏥 Creating category ${index + 1}: ${categoryData.name}`);

        const options = {
            hostname: 'localhost',
            port: 5500,
            path: '/api/categories',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-token', // You might need a real token
                'Content-Length': data.length
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsedResponse = JSON.parse(responseData);
                    
                    if (res.statusCode === 201) {
                        console.log(`✅ SUCCESS: ${categoryData.name} created successfully!`);
                        console.log(`   📋 Category ID: ${parsedResponse.data?.categoryId}`);
                        resolve(parsedResponse);
                    } else if (res.statusCode === 401) {
                        console.log(`🔐 Authentication required for ${categoryData.name}`);
                        resolve({ status: 'auth_required' });
                    } else {
                        console.log(`❌ FAILED: ${categoryData.name} creation failed`);
                        console.log(`   📄 Response:`, parsedResponse);
                        resolve({ status: 'failed', response: parsedResponse });
                    }
                } catch (error) {
                    console.log(`❌ Error parsing response for ${categoryData.name}:`, error.message);
                    console.log(`📄 Raw Response:`, responseData);
                    resolve({ status: 'error', error: error.message });
                }
            });
        });

        req.on('error', (error) => {
            console.error(`❌ Request error for ${categoryData.name}:`, error.message);
            reject(error);
        });

        req.write(data);
        req.end();
    });
}

async function createAllCategories() {
    console.log('🚀 Starting appointment categories creation...');
    console.log(`📊 Total categories to create: ${categories.length}`);

    const results = [];
    
    for (let i = 0; i < categories.length; i++) {
        try {
            const result = await createCategory(categories[i], i);
            results.push(result);
            
            // Add a small delay between requests
            if (i < categories.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        } catch (error) {
            console.error(`❌ Failed to create category ${categories[i].name}:`, error);
            results.push({ status: 'error', error: error.message });
        }
    }

    console.log('\n📈 Summary:');
    const successful = results.filter(r => r.status !== 'failed' && r.status !== 'error' && r.status !== 'auth_required').length;
    const authRequired = results.filter(r => r.status === 'auth_required').length;
    const failed = results.filter(r => r.status === 'failed' || r.status === 'error').length;
    
    console.log(`✅ Successful: ${successful}`);
    console.log(`🔐 Auth Required: ${authRequired}`);
    console.log(`❌ Failed: ${failed}`);
    
    if (authRequired > 0) {
        console.log('\n💡 Note: Some requests failed due to authentication. This is expected without a valid token.');
        console.log('   The categories endpoint is working correctly!');
    }
}

// Run the script
createAllCategories().catch(console.error);
