const http = require('http');

// Test service creation without appointmentId
function testServiceCreation() {
  const serviceData = {
    serviceName: 'Test Service Fix',
    description: 'Testing service creation after fixing NaN error',
    serviceCategoryId: 1001, // Use a valid service category ID
    defaultPrice: 100,
    estimatedDuration: 30,
    currency: 'KES',
    isActive: true,
    isCustom: true
  };

  const data = JSON.stringify(serviceData);

  console.log('\n🧪 Testing service creation after NaN fix...');
  console.log('📝 Service data:', serviceData);

  const options = {
    hostname: 'localhost',
    port: 5500,
    path: '/api/services',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token', // You might need a real token
      'Content-Length': data.length
    }
  };

  const req = http.request(options, (res) => {
    let responseData = '';

    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      console.log('\n📡 Response Status:', res.statusCode);
      console.log('📄 Response Headers:', res.headers);

      try {
        const parsedResponse = JSON.parse(responseData);
        console.log('✅ Response Body:', JSON.stringify(parsedResponse, null, 2));

        if (res.statusCode === 201) {
          console.log('🎉 SUCCESS: Service created successfully!');
        } else if (res.statusCode === 401) {
          console.log('🔐 Authentication required - this is expected without a valid token');
        } else {
          console.log('❌ FAILED: Service creation failed');
        }
      } catch (error) {
        console.log('📄 Raw Response:', responseData);
        console.log('❌ Error parsing response:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request error:', error.message);
  });

  req.write(data);
  req.end();
}

// Test with appointmentId to see if it gets properly removed
function testServiceCreationWithAppointmentId() {
  const serviceData = {
    serviceName: 'Test Service with AppointmentId',
    description: 'Testing service creation with appointmentId that should be removed',
    serviceCategoryId: 1001,
    defaultPrice: 150,
    estimatedDuration: 45,
    currency: 'KES',
    appointmentId: 'invalid-value', // This should be removed and not cause NaN error
    isActive: true,
    isCustom: true
  };

  const data = JSON.stringify(serviceData);

  console.log('\n🧪 Testing service creation with appointmentId (should be removed)...');
  console.log('📝 Service data:', serviceData);

  const options = {
    hostname: 'localhost',
    port: 5500,
    path: '/api/services',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token',
      'Content-Length': data.length
    }
  };

  const req = http.request(options, (res) => {
    let responseData = '';

    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      console.log('\n📡 Response Status:', res.statusCode);

      try {
        const parsedResponse = JSON.parse(responseData);
        console.log('✅ Response Body:', JSON.stringify(parsedResponse, null, 2));

        if (res.statusCode === 201) {
          console.log('🎉 SUCCESS: Service created successfully even with appointmentId in request!');
        } else if (res.statusCode === 401) {
          console.log('🔐 Authentication required - this is expected without a valid token');
        } else if (res.statusCode !== 500) {
          console.log('✅ GOOD: No 500 error with NaN - the fix is working!');
        } else {
          console.log('❌ FAILED: Still getting 500 error');
        }
      } catch (error) {
        console.log('📄 Raw Response:', responseData);
        console.log('❌ Error parsing response:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request error:', error.message);
  });

  req.write(data);
  req.end();
}

// Test invalid appointmentId to verify NaN error is fixed
function testInvalidAppointmentId() {
  console.log('\n🧪 Testing invalid appointmentId handling...');

  const options = {
    hostname: 'localhost',
    port: 5500,
    path: '/api/appointments/invalid-id',
    method: 'GET',
    headers: {
      'Authorization': 'Bearer test-token'
    }
  };

  const req = http.request(options, (res) => {
    let responseData = '';

    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      console.log('\n📡 Response Status:', res.statusCode);

      try {
        const parsedResponse = JSON.parse(responseData);
        console.log('✅ Response Body:', JSON.stringify(parsedResponse, null, 2));

        if (res.statusCode === 400 && parsedResponse.message === "Invalid appointment ID provided") {
          console.log('🎉 SUCCESS: Invalid appointmentId properly handled with 400 error!');
        } else if (res.statusCode === 401) {
          console.log('🔐 Authentication required - but no 500 NaN error, which is good!');
        } else if (res.statusCode !== 500) {
          console.log('✅ GOOD: No 500 NaN error - the fix is working!');
        } else {
          console.log('❌ FAILED: Still getting 500 error');
        }
      } catch (error) {
        console.log('📄 Raw Response:', responseData);
        console.log('❌ Error parsing response:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request error:', error.message);
  });

  req.end();
}

// Test invoice generation with invalid appointmentId
function testInvalidInvoiceGeneration() {
  console.log('\n🧪 Testing invoice generation with invalid appointmentId...');

  const options = {
    hostname: 'localhost',
    port: 5500,
    path: '/api/invoices/appointment/not-a-number/generate',
    method: 'POST',
    headers: {
      'Authorization': 'Bearer test-token',
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    let responseData = '';

    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      console.log('\n📡 Response Status:', res.statusCode);

      try {
        const parsedResponse = JSON.parse(responseData);
        console.log('✅ Response Body:', JSON.stringify(parsedResponse, null, 2));

        if (res.statusCode === 400 && parsedResponse.message === "Invalid appointment ID provided") {
          console.log('🎉 SUCCESS: Invalid appointmentId in invoice generation properly handled!');
        } else if (res.statusCode === 401) {
          console.log('🔐 Authentication required - but no 500 NaN error, which is good!');
        } else if (res.statusCode !== 500) {
          console.log('✅ GOOD: No 500 NaN error - the fix is working!');
        } else {
          console.log('❌ FAILED: Still getting 500 error');
        }
      } catch (error) {
        console.log('📄 Raw Response:', responseData);
        console.log('❌ Error parsing response:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request error:', error.message);
  });

  req.end();
}

// Run tests
console.log('🚀 Starting comprehensive NaN fix tests...');
testServiceCreation();

setTimeout(() => {
  testServiceCreationWithAppointmentId();
}, 1000);

setTimeout(() => {
  testInvalidAppointmentId();
}, 2000);

setTimeout(() => {
  testInvalidInvoiceGeneration();
}, 3000);
