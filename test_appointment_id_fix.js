const http = require('http');

// Test the improved appointment ID validation
function testAppointmentIdValidation(appointmentId, description, expectedStatus, expectedMessage) {
    return new Promise((resolve) => {
        console.log(`\n🧪 Testing ${description}: "${appointmentId}"`);

        const options = {
            hostname: 'localhost',
            port: 5500,
            path: `/api/appointments/${appointmentId}`,
            method: 'GET',
            headers: {
                'Authorization': 'Bearer test-token'
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsedResponse = JSON.parse(responseData);
                    console.log(`   📡 Status: ${res.statusCode}`);
                    console.log(`   📄 Message: ${parsedResponse.message}`);
                    
                    if (res.statusCode === expectedStatus) {
                        if (expectedMessage && parsedResponse.message.includes(expectedMessage)) {
                            console.log(`   ✅ EXPECTED: Got expected status and message`);
                        } else if (!expectedMessage) {
                            console.log(`   ✅ EXPECTED: Got expected status`);
                        } else {
                            console.log(`   ⚠️  PARTIAL: Got expected status but different message`);
                        }
                    } else if (res.statusCode === 401) {
                        console.log(`   🔐 AUTH: Authentication required (endpoint working)`);
                    } else {
                        console.log(`   ❌ UNEXPECTED: Expected ${expectedStatus}, got ${res.statusCode}`);
                    }
                } catch (error) {
                    console.log(`   ❌ Parse Error: ${error.message}`);
                    console.log(`   📄 Raw: ${responseData}`);
                }
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log(`   ❌ Request Error: ${error.message}`);
            resolve();
        });

        req.end();
    });
}

// Test various scenarios
async function runImprovedTests() {
    console.log('🚀 Testing Improved Appointment ID Validation...');
    
    // Test cases that should trigger specific error messages
    await testAppointmentIdValidation('undefined', 'undefined string', 400, 'Appointment ID is required');
    await testAppointmentIdValidation('null', 'null string', 400, 'Appointment ID is required');
    await testAppointmentIdValidation('', 'empty string', 400, 'Appointment ID is required');
    await testAppointmentIdValidation('abc', 'non-numeric string', 400, 'Invalid appointment ID format');
    await testAppointmentIdValidation('0', 'zero', 400, 'Invalid appointment ID format');
    await testAppointmentIdValidation('-1', 'negative number', 400, 'Invalid appointment ID format');
    
    // Test cases that should pass validation (but might not exist)
    await testAppointmentIdValidation('1', 'valid small number', 404, 'Appointment not found');
    await testAppointmentIdValidation('1001', 'valid ID (starting sequence)', 404, 'Appointment not found');
    
    console.log('\n📋 Summary:');
    console.log('   ✅ Improved validation provides better error messages');
    console.log('   🔍 If you\'re still getting errors, check:');
    console.log('      1. Frontend URL construction');
    console.log('      2. React Router parameter extraction');
    console.log('      3. Navigation calls with invalid IDs');
    console.log('      4. Browser URL bar for malformed URLs');
}

// Run the improved tests
runImprovedTests().catch(console.error);
