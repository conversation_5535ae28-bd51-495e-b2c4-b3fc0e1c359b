const http = require('http');

// Test getting all categories
function testGetCategories() {
    console.log('🧪 Testing GET /api/categories endpoint...');

    const options = {
        hostname: 'localhost',
        port: 5500,
        path: '/api/categories?limit=20',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer test-token'
        }
    };

    const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
            responseData += chunk;
        });

        res.on('end', () => {
            console.log('\n📡 Response Status:', res.statusCode);
            
            try {
                const parsedResponse = JSON.parse(responseData);
                
                if (res.statusCode === 401) {
                    console.log('🔐 Authentication required - this is expected');
                    console.log('✅ Categories endpoint is working and properly protected!');
                } else if (res.statusCode === 200) {
                    console.log('🎉 SUCCESS: Categories retrieved successfully!');
                    console.log('📊 Response:', JSON.stringify(parsedResponse, null, 2));
                    
                    if (parsedResponse.data && parsedResponse.data.categories) {
                        console.log(`\n📋 Found ${parsedResponse.data.categories.length} categories:`);
                        parsedResponse.data.categories.forEach((category, index) => {
                            console.log(`   ${index + 1}. ${category.name} (ID: ${category.categoryId}) - ${category.color}`);
                        });
                    }
                } else {
                    console.log('⚠️  Unexpected response:', parsedResponse);
                }
            } catch (error) {
                console.log('📄 Raw Response:', responseData);
                console.log('❌ Error parsing response:', error.message);
            }
        });
    });

    req.on('error', (error) => {
        console.error('❌ Request error:', error.message);
    });

    req.end();
}

// Test getting categories with stats
function testGetCategoriesWithStats() {
    console.log('\n🧪 Testing GET /api/categories/stats endpoint...');

    const options = {
        hostname: 'localhost',
        port: 5500,
        path: '/api/categories/stats',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer test-token'
        }
    };

    const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
            responseData += chunk;
        });

        res.on('end', () => {
            console.log('\n📡 Response Status:', res.statusCode);
            
            try {
                const parsedResponse = JSON.parse(responseData);
                
                if (res.statusCode === 401) {
                    console.log('🔐 Authentication required - this is expected');
                    console.log('✅ Categories stats endpoint is working and properly protected!');
                } else if (res.statusCode === 200) {
                    console.log('🎉 SUCCESS: Categories with stats retrieved successfully!');
                    console.log('📊 Response:', JSON.stringify(parsedResponse, null, 2));
                } else {
                    console.log('⚠️  Unexpected response:', parsedResponse);
                }
            } catch (error) {
                console.log('📄 Raw Response:', responseData);
                console.log('❌ Error parsing response:', error.message);
            }
        });
    });

    req.on('error', (error) => {
        console.error('❌ Request error:', error.message);
    });

    req.end();
}

// Run tests
console.log('🚀 Testing categories endpoints...');
testGetCategories();

setTimeout(() => {
    testGetCategoriesWithStats();
}, 2000);
