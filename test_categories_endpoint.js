const http = require('http');

// Test if the categories endpoint is accessible
function testCategoriesEndpoint() {
    console.log('🧪 Testing categories endpoint accessibility...');

    const options = {
        hostname: 'localhost',
        port: 5500,
        path: '/api/categories',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer test-token'
        }
    };

    const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
            responseData += chunk;
        });

        res.on('end', () => {
            console.log('\n📡 Response Status:', res.statusCode);
            
            try {
                const parsedResponse = JSON.parse(responseData);
                console.log('✅ Response Body:', JSON.stringify(parsedResponse, null, 2));
                
                if (res.statusCode === 401) {
                    console.log('🎉 SUCCESS: Categories endpoint is working! (401 = authentication required)');
                    console.log('💡 The endpoint is properly protected and accessible at /api/categories');
                } else if (res.statusCode === 200) {
                    console.log('🎉 SUCCESS: Categories endpoint is working and returned data!');
                } else {
                    console.log('⚠️  Unexpected response code, but endpoint is responding');
                }
            } catch (error) {
                console.log('📄 Raw Response:', responseData);
                console.log('❌ Error parsing response:', error.message);
            }
        });
    });

    req.on('error', (error) => {
        console.error('❌ Request error:', error.message);
    });

    req.end();
}

// Test endpoint accessibility
testCategoriesEndpoint();
