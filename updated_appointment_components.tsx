// Example: How to update your components to fetch all appointments instead of by ID

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getAppointments } from '@/services/appointments';

// 1. REPLACE AppointmentDetails component to show all appointments
const AllAppointments: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('today');

  // Calculate date range based on filter
  const getDateRange = () => {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    switch (dateFilter) {
      case 'today':
        return { startDate: todayStr, endDate: todayStr };
      case 'week':
        const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
        const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
        return { 
          startDate: weekStart.toISOString().split('T')[0], 
          endDate: weekEnd.toISOString().split('T')[0] 
        };
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        return { 
          startDate: monthStart.toISOString().split('T')[0], 
          endDate: monthEnd.toISOString().split('T')[0] 
        };
      default:
        return {};
    }
  };

  // Fetch appointments with filters
  const { data: appointmentsResponse, isLoading, error, refetch } = useQuery({
    queryKey: ['appointments', currentPage, statusFilter, dateFilter],
    queryFn: () => {
      const dateRange = getDateRange();
      return getAppointments({
        page: currentPage,
        limit: 20,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        ...dateRange,
        sortBy: 'appointmentDate',
        sortOrder: 'desc'
      });
    }
  });

  if (isLoading) return <div className="p-6">Loading appointments...</div>;
  if (error) return <div className="p-6 text-red-600">Error loading appointments</div>;

  const appointments = appointmentsResponse?.data?.data || [];
  const pagination = appointmentsResponse?.data?.pagination;

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">All Appointments</h1>
        <button 
          onClick={() => refetch()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Refresh
        </button>
      </div>

      {/* Filters */}
      <div className="flex gap-4 mb-6">
        <select 
          value={statusFilter} 
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border rounded"
        >
          <option value="all">All Status</option>
          <option value="scheduled">Scheduled</option>
          <option value="in_progress">In Progress</option>
          <option value="completed">Completed</option>
          <option value="cancelled">Cancelled</option>
        </select>

        <select 
          value={dateFilter} 
          onChange={(e) => setDateFilter(e.target.value)}
          className="px-3 py-2 border rounded"
        >
          <option value="all">All Dates</option>
          <option value="today">Today</option>
          <option value="week">This Week</option>
          <option value="month">This Month</option>
        </select>
      </div>

      {/* Appointments List */}
      <div className="grid gap-4">
        {appointments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No appointments found with current filters
          </div>
        ) : (
          appointments.map((appointment) => (
            <div key={appointment.appointmentId} className="border rounded-lg p-4 hover:shadow-md">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-lg">
                    Appointment #{appointment.appointmentId}
                  </h3>
                  <p className="text-gray-600">
                    Date: {new Date(appointment.appointmentDate).toLocaleString()}
                  </p>
                  <p className="text-gray-600">Pet ID: {appointment.petId}</p>
                  <p className="text-gray-600">Client ID: {appointment.clientId}</p>
                  {appointment.completionPercentage !== undefined && (
                    <p className="text-gray-600">
                      Progress: {appointment.completionPercentage}%
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded text-sm ${
                    appointment.status === 'completed' ? 'bg-green-100 text-green-800' :
                    appointment.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                    appointment.status === 'scheduled' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {appointment.status}
                  </span>
                  <div className="mt-2">
                    <button 
                      onClick={() => window.location.href = `/appointments/${appointment.appointmentId}`}
                      className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-6">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          <span className="px-3 py-1">
            Page {currentPage} of {pagination.totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(pagination.totalPages, currentPage + 1))}
            disabled={currentPage === pagination.totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}

      {/* Summary */}
      <div className="mt-6 text-center text-gray-600">
        Showing {appointments.length} of {pagination?.totalCount || 0} appointments
      </div>
    </div>
  );
};

// 2. CURRENT/TODAY'S APPOINTMENTS COMPONENT
const CurrentAppointments: React.FC = () => {
  const { data: appointmentsResponse, isLoading } = useQuery({
    queryKey: ['appointments', 'current'],
    queryFn: () => {
      const today = new Date().toISOString().split('T')[0];
      return getAppointments({
        page: 1,
        limit: 50,
        status: 'in_progress',
        startDate: today,
        endDate: today,
        sortBy: 'appointmentDate',
        sortOrder: 'asc'
      });
    },
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  if (isLoading) return <div>Loading current appointments...</div>;

  const appointments = appointmentsResponse?.data?.data || [];

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Current Appointments ({appointments.length})</h2>
      <div className="space-y-3">
        {appointments.map((appointment) => (
          <div key={appointment.appointmentId} className="border-l-4 border-blue-500 pl-4 py-2">
            <div className="font-semibold">Appointment #{appointment.appointmentId}</div>
            <div className="text-sm text-gray-600">
              {new Date(appointment.appointmentDate).toLocaleTimeString()} - 
              Pet: {appointment.petId} | Client: {appointment.clientId}
            </div>
            {appointment.completionPercentage !== undefined && (
              <div className="text-sm">
                Progress: {appointment.completionPercentage}% complete
              </div>
            )}
          </div>
        ))}
        {appointments.length === 0 && (
          <div className="text-gray-500 text-center py-4">
            No appointments in progress today
          </div>
        )}
      </div>
    </div>
  );
};

// 3. APPOINTMENTS BY CLINIC
const ClinicAppointments: React.FC<{ clinicId: number }> = ({ clinicId }) => {
  const { data: appointmentsResponse, isLoading } = useQuery({
    queryKey: ['appointments', 'clinic', clinicId],
    queryFn: () => getAppointments({
      page: 1,
      limit: 100,
      clinicId: clinicId.toString(),
      sortBy: 'appointmentDate',
      sortOrder: 'desc'
    })
  });

  if (isLoading) return <div>Loading clinic appointments...</div>;

  const appointments = appointmentsResponse?.data?.data || [];

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">
        Clinic Appointments ({appointments.length})
      </h2>
      {/* Render appointments list */}
    </div>
  );
};

export { AllAppointments, CurrentAppointments, ClinicAppointments };
