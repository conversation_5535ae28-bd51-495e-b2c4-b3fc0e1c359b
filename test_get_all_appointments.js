const http = require('http');

// Test fetching all appointments with different parameters
function testGetAllAppointments(params = {}, description = 'basic fetch') {
    return new Promise((resolve) => {
        const queryParams = new URLSearchParams();
        
        // Default parameters
        queryParams.append('page', String(params.page || 1));
        queryParams.append('limit', String(params.limit || 10));
        
        // Add other parameters
        Object.entries(params).forEach(([key, value]) => {
            if (value && !['page', 'limit'].includes(key)) {
                queryParams.append(key, String(value));
            }
        });

        const url = `/api/appointments?${queryParams.toString()}`;
        console.log(`\n🧪 Testing ${description}`);
        console.log(`   🌐 URL: ${url}`);

        const options = {
            hostname: 'localhost',
            port: 5500,
            path: url,
            method: 'GET',
            headers: {
                'Authorization': 'Bearer test-token'
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsedResponse = JSON.parse(responseData);
                    console.log(`   📡 Status: ${res.statusCode}`);
                    
                    if (res.statusCode === 200 && parsedResponse.success) {
                        console.log(`   ✅ SUCCESS: ${parsedResponse.message}`);
                        if (parsedResponse.data && parsedResponse.data.data) {
                            console.log(`   📊 Found ${parsedResponse.data.data.length} appointments`);
                            console.log(`   📄 Total Count: ${parsedResponse.data.pagination?.totalCount || 'N/A'}`);
                            
                            // Show first appointment as example
                            if (parsedResponse.data.data.length > 0) {
                                const firstAppointment = parsedResponse.data.data[0];
                                console.log(`   📋 Example: Appointment #${firstAppointment.appointmentId} - Status: ${firstAppointment.status}`);
                            }
                        }
                    } else if (res.statusCode === 401) {
                        console.log(`   🔐 AUTH: Authentication required (endpoint working)`);
                    } else {
                        console.log(`   ❌ FAILED: ${parsedResponse.message}`);
                    }
                } catch (error) {
                    console.log(`   ❌ Parse Error: ${error.message}`);
                    console.log(`   📄 Raw: ${responseData.substring(0, 200)}...`);
                }
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log(`   ❌ Request Error: ${error.message}`);
            resolve();
        });

        req.end();
    });
}

// Test various appointment fetching scenarios
async function runAppointmentTests() {
    console.log('🚀 Testing Get All Appointments API...');
    
    // 1. Basic fetch all
    await testGetAllAppointments({}, 'basic fetch all appointments');
    
    // 2. Fetch with pagination
    await testGetAllAppointments({
        page: 1,
        limit: 5
    }, 'fetch with pagination (5 per page)');
    
    // 3. Fetch by status
    await testGetAllAppointments({
        status: 'in_progress',
        limit: 20
    }, 'fetch in-progress appointments');
    
    // 4. Fetch by date range (today)
    const today = new Date().toISOString().split('T')[0];
    await testGetAllAppointments({
        startDate: today,
        endDate: today,
        limit: 50
    }, 'fetch today\'s appointments');
    
    // 5. Fetch with sorting
    await testGetAllAppointments({
        sortBy: 'appointmentDate',
        sortOrder: 'desc',
        limit: 10
    }, 'fetch with date sorting (newest first)');
    
    // 6. Fetch by clinic (if you have clinicId)
    await testGetAllAppointments({
        clinicId: 1001,
        limit: 20
    }, 'fetch by clinic ID');
    
    // 7. Fetch current appointments
    await testGetAllAppointments({
        status: 'in_progress',
        startDate: today,
        limit: 30
    }, 'fetch current appointments (in-progress today)');
    
    console.log('\n📋 Summary:');
    console.log('   ✅ Use getAppointments() instead of getAppointmentById()');
    console.log('   🔍 Available filters:');
    console.log('      - status: "scheduled", "in_progress", "completed", "cancelled"');
    console.log('      - priority: "low", "normal", "high", "emergency"');
    console.log('      - clinicId: filter by clinic');
    console.log('      - petId: filter by pet');
    console.log('      - clientId: filter by client');
    console.log('      - staffInCharge: filter by staff member');
    console.log('      - startDate/endDate: date range filtering');
    console.log('      - page/limit: pagination');
    console.log('      - sortBy/sortOrder: sorting options');
}

// Run the tests
runAppointmentTests().catch(console.error);
